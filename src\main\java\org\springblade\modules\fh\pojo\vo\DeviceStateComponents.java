package org.springblade.modules.fh.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 设备物模型状态组件类
 * 包含设备物模型状态中的各种组件类
 *
 * <AUTHOR> AI
 */
public class DeviceStateComponents {

    // 将这些类导出，方便在其他地方使用
    public static final Class<?>[] COMPONENT_CLASSES = {
        AirConditioner.class, AlternateLandPoint.class, BackupBattery.class, Battery.class,
        DroneBatteryMaintenanceInfo.class, DroneChargeState.class, LiveCapacity.class,
        LiveDevice.class, LiveCamera.class, LiveVideo.class, LiveStatus.class,
        MaintainStatus.class, MaintainStatusItem.class, MediaFileDetail.class,
        NetworkState.class, PositionState.class, RtcmInfo.class, Storage.class,
        SubDevice.class, WirelessLink.class, WirelessLinkTopo.class, CenterNode.class,
        LeafNode.class
    };

    /**
     * 空调状态
     */
    @Data
    @Schema(description = "空调状态")
    public static class AirConditioner {
        /**
         * 空调状态
         */
        @Schema(description = "空调状态")
        @JsonProperty("air_conditioner_state")
        private String airConditionerState;

        /**
         * 切换时间
         */
        @Schema(description = "切换时间")
        @JsonProperty("switch_time")
        private Long switchTime;
    }

    /**
     * 备用降落点信息
     */
    @Data
    @Schema(description = "备用降落点信息")
    public static class AlternateLandPoint {
        /**
         * 高度
         */
        @Schema(description = "高度")
        private Double height;

        /**
         * 是否已配置
         */
        @Schema(description = "是否已配置")
        @JsonProperty("is_configured")
        private String isConfigured;

        /**
         * 纬度
         */
        @Schema(description = "纬度")
        private Double latitude;

        /**
         * 经度
         */
        @Schema(description = "经度")
        private Double longitude;

        /**
         * 安全降落高度
         */
        @Schema(description = "安全降落高度")
        @JsonProperty("safe_land_height")
        private Integer safeLandHeight;
    }

    /**
     * 备用电池信息
     */
    @Data
    @Schema(description = "备用电池信息")
    public static class BackupBattery {
        /**
         * 开关状态
         */
        @Schema(description = "开关状态")
        private String switchState;

        /**
         * 温度
         */
        @Schema(description = "温度")
        private Double temperature;

        /**
         * 电压
         */
        @Schema(description = "电压")
        private Integer voltage;
    }

    /**
     * 电池信息
     */
    @Data
    @Schema(description = "电池信息")
    public static class Battery {
        /**
         * 电量百分比
         */
        @Schema(description = "电量百分比")
        @JsonProperty("capacity_percent")
        private Integer capacityPercent;

        /**
         * 电池索引
         */
        @Schema(description = "电池索引")
        private String index;

        /**
         * 温度
         */
        @Schema(description = "温度")
        private Double temperature;

        /**
         * 电压
         */
        @Schema(description = "电压")
        private Integer voltage;
    }

    /**
     * 无人机电池维护信息
     */
    @Data
    @Schema(description = "无人机电池维护信息")
    public static class DroneBatteryMaintenanceInfo {
        /**
         * 电池列表
         */
        @Schema(description = "电池列表")
        private List<Battery> batteries;

        /**
         * 加热状态
         */
        @Schema(description = "加热状态")
        @JsonProperty("heat_state")
        private String heatState;

        /**
         * 维护状态
         */
        @Schema(description = "维护状态")
        @JsonProperty("maintenance_state")
        private String maintenanceState;

        /**
         * 维护剩余时间
         */
        @Schema(description = "维护剩余时间")
        @JsonProperty("maintenance_time_left")
        private Integer maintenanceTimeLeft;
    }

    /**
     * 无人机充电状态
     */
    @Data
    @Schema(description = "无人机充电状态")
    public static class DroneChargeState {
        /**
         * 电量百分比
         */
        @Schema(description = "电量百分比")
        @JsonProperty("capacity_percent")
        private Integer capacityPercent;

        /**
         * 状态
         */
        @Schema(description = "状态")
        private String state;
    }

    /**
     * 直播能力
     */
    @Data
    @Schema(description = "直播能力")
    public static class LiveCapacity {
        /**
         * 可用视频数量
         */
        @Schema(description = "可用视频数量")
        @JsonProperty("available_video_number")
        private Integer availableVideoNumber;

        /**
         * 最大共存视频数量
         */
        @Schema(description = "最大共存视频数量")
        @JsonProperty("coexist_video_number_max")
        private Integer coexistVideoNumberMax;

        /**
         * 设备列表
         */
        @Schema(description = "设备列表")
        @JsonProperty("device_list")
        private List<LiveDevice> deviceList;
    }

    /**
     * 直播设备
     */
    @Data
    @Schema(description = "直播设备")
    public static class LiveDevice {
        /**
         * 可用视频数量
         */
        @Schema(description = "可用视频数量")
        @JsonProperty("available_video_number")
        private Integer availableVideoNumber;

        /**
         * 相机列表
         */
        @Schema(description = "相机列表")
        @JsonProperty("camera_list")
        private List<LiveCamera> cameraList;

        /**
         * 最大共存视频数量
         */
        @Schema(description = "最大共存视频数量")
        @JsonProperty("coexist_video_number_max")
        private Integer coexistVideoNumberMax;

        /**
         * 设备SN
         */
        @Schema(description = "设备SN")
        private String sn;
    }

    /**
     * 直播相机
     */
    @Data
    @Schema(description = "直播相机")
    public static class LiveCamera {
        /**
         * 可用相机位置
         */
        @Schema(description = "可用相机位置")
        @JsonProperty("availabe_camera_positions")
        private List<Integer> availabeCameraPositions;

        /**
         * 可用视频数量
         */
        @Schema(description = "可用视频数量")
        @JsonProperty("available_video_number")
        private Integer availableVideoNumber;

        /**
         * 相机索引
         */
        @Schema(description = "相机索引")
        @JsonProperty("camera_index")
        private String cameraIndex;

        /**
         * 相机位置
         */
        @Schema(description = "相机位置")
        @JsonProperty("camera_position")
        private Integer cameraPosition;

        /**
         * 最大共存视频数量
         */
        @Schema(description = "最大共存视频数量")
        @JsonProperty("coexist_video_number_max")
        private Integer coexistVideoNumberMax;

        /**
         * 视频列表
         */
        @Schema(description = "视频列表")
        @JsonProperty("video_list")
        private List<LiveVideo> videoList;
    }

    /**
     * 直播视频
     */
    @Data
    @Schema(description = "直播视频")
    public static class LiveVideo {
        /**
         * 可切换视频类型
         */
        @Schema(description = "可切换视频类型")
        @JsonProperty("switchable_video_types")
        private List<String> switchableVideoTypes;

        /**
         * 视频索引
         */
        @Schema(description = "视频索引")
        @JsonProperty("video_index")
        private String videoIndex;

        /**
         * 视频类型
         */
        @Schema(description = "视频类型")
        @JsonProperty("video_type")
        private String videoType;
    }

    /**
     * 直播状态
     */
    @Data
    @Schema(description = "直播状态")
    public static class LiveStatus {
        /**
         * 错误状态
         */
        @Schema(description = "错误状态")
        @JsonProperty("error_status")
        private Integer errorStatus;

        /**
         * 状态
         */
        @Schema(description = "状态")
        private String status;

        /**
         * 视频ID
         */
        @Schema(description = "视频ID")
        @JsonProperty("video_id")
        private String videoId;

        /**
         * 视频质量
         */
        @Schema(description = "视频质量")
        @JsonProperty("video_quality")
        private String videoQuality;

        /**
         * 视频类型
         */
        @Schema(description = "视频类型")
        @JsonProperty("video_type")
        private String videoType;
    }

    /**
     * 维护状态
     */
    @Data
    @Schema(description = "维护状态")
    public static class MaintainStatus {
        /**
         * 维护状态数组
         */
        @Schema(description = "维护状态数组")
        @JsonProperty("maintain_status_array")
        private List<MaintainStatusItem> maintainStatusArray;
    }

    /**
     * 维护状态项
     */
    @Data
    @Schema(description = "维护状态项")
    public static class MaintainStatusItem {
        /**
         * 上次维护时间
         */
        @Schema(description = "上次维护时间")
        @JsonProperty("last_maintain_time")
        private Long lastMaintainTime;

        /**
         * 上次维护类型
         */
        @Schema(description = "上次维护类型")
        @JsonProperty("last_maintain_type")
        private String lastMaintainType;

        /**
         * 上次维护工作架次
         */
        @Schema(description = "上次维护工作架次")
        @JsonProperty("last_maintain_work_sorties")
        private Integer lastMaintainWorkSorties;

        /**
         * 状态
         */
        @Schema(description = "状态")
        private String state;
    }

    /**
     * 媒体文件详情
     */
    @Data
    @Schema(description = "媒体文件详情")
    public static class MediaFileDetail {
        /**
         * 剩余上传数量
         */
        @Schema(description = "剩余上传数量")
        @JsonProperty("remain_upload")
        private Integer remainUpload;
    }

    /**
     * 网络状态
     */
    @Data
    @Schema(description = "网络状态")
    public static class NetworkState {
        /**
         * 网络质量
         */
        @Schema(description = "网络质量")
        private String quality;

        /**
         * 网络速率
         */
        @Schema(description = "网络速率")
        private Integer rate;

        /**
         * 网络类型
         */
        @Schema(description = "网络类型")
        private String type;
    }

    /**
     * 位置状态
     */
    @Data
    @Schema(description = "位置状态")
    public static class PositionState {
        /**
         * GPS卫星数量
         */
        @Schema(description = "GPS卫星数量")
        @JsonProperty("gps_number")
        private Integer gpsNumber;

        /**
         * 是否已校准
         */
        @Schema(description = "是否已校准")
        @JsonProperty("is_calibration")
        private String isCalibration;

        /**
         * 是否已定位
         */
        @Schema(description = "是否已定位")
        @JsonProperty("is_fixed")
        private String isFixed;

        /**
         * 定位质量
         */
        @Schema(description = "定位质量")
        private String quality;

        /**
         * RTK卫星数量
         */
        @Schema(description = "RTK卫星数量")
        @JsonProperty("rtk_number")
        private Integer rtkNumber;
    }

    /**
     * RTCM信息
     */
    @Data
    @Schema(description = "RTCM信息")
    public static class RtcmInfo {
        /**
         * 主机地址
         */
        @Schema(description = "主机地址")
        private String host;

        /**
         * 挂载点
         */
        @Schema(description = "挂载点")
        @JsonProperty("mount_point")
        private String mountPoint;

        /**
         * 端口
         */
        @Schema(description = "端口")
        private String port;

        /**
         * RTCM设备类型
         */
        @Schema(description = "RTCM设备类型")
        @JsonProperty("rtcm_device_type")
        private String rtcmDeviceType;

        /**
         * 源类型
         */
        @Schema(description = "源类型")
        @JsonProperty("source_type")
        private String sourceType;
    }

    /**
     * 存储信息
     */
    @Data
    @Schema(description = "存储信息")
    public static class Storage {
        /**
         * 总容量
         */
        @Schema(description = "总容量")
        private Long total;

        /**
         * 已使用容量
         */
        @Schema(description = "已使用容量")
        private Long used;
    }

    /**
     * 子设备信息
     */
    @Data
    @Schema(description = "子设备信息")
    public static class SubDevice {
        /**
         * 设备在线状态
         */
        @Schema(description = "设备在线状态")
        @JsonProperty("device_online_status")
        private String deviceOnlineStatus;

        /**
         * 设备是否已配对
         */
        @Schema(description = "设备是否已配对")
        @JsonProperty("device_paired")
        private String devicePaired;

        /**
         * 设备SN
         */
        @Schema(description = "设备SN")
        @JsonProperty("device_sn")
        private String deviceSn;
    }

    /**
     * 无线链路
     */
    @Data
    @Schema(description = "无线链路")
    public static class WirelessLink {
        /**
         * 4G频段
         */
        @Schema(description = "4G频段")
        @JsonProperty("4g_freq_band")
        private Double fourGFreqBand;

        /**
         * 4G地面质量
         */
        @Schema(description = "4G地面质量")
        @JsonProperty("4g_gnd_quality")
        private Integer fourGGndQuality;

        /**
         * 4G链路状态
         */
        @Schema(description = "4G链路状态")
        @JsonProperty("4g_link_state")
        private String fourGLinkState;

        /**
         * 4G质量
         */
        @Schema(description = "4G质量")
        @JsonProperty("4g_quality")
        private Integer fourGQuality;

        /**
         * 4G无人机质量
         */
        @Schema(description = "4G无人机质量")
        @JsonProperty("4g_uav_quality")
        private Integer fourGUavQuality;

        /**
         * 加密狗数量
         */
        @Schema(description = "加密狗数量")
        @JsonProperty("dongle_number")
        private Integer dongleNumber;

        /**
         * 链路工作模式
         */
        @Schema(description = "链路工作模式")
        @JsonProperty("link_workmode")
        private String linkWorkmode;

        /**
         * SDR频段
         */
        @Schema(description = "SDR频段")
        @JsonProperty("sdr_freq_band")
        private Double sdrFreqBand;

        /**
         * SDR链路状态
         */
        @Schema(description = "SDR链路状态")
        @JsonProperty("sdr_link_state")
        private String sdrLinkState;

        /**
         * SDR质量
         */
        @Schema(description = "SDR质量")
        @JsonProperty("sdr_quality")
        private Integer sdrQuality;
    }

    /**
     * 无线链路拓扑
     */
    @Data
    @Schema(description = "无线链路拓扑")
    public static class WirelessLinkTopo {
        /**
         * 中心节点
         */
        @Schema(description = "中心节点")
        @JsonProperty("center_node")
        private CenterNode centerNode;

        /**
         * 叶子节点
         */
        @Schema(description = "叶子节点")
        @JsonProperty("leaf_nodes")
        private List<LeafNode> leafNodes;

        /**
         * 密钥
         */
        @Schema(description = "密钥")
        @JsonProperty("secret_code")
        private List<Integer> secretCode;
    }

    /**
     * 中心节点
     */
    @Data
    @Schema(description = "中心节点")
    public static class CenterNode {
        /**
         * SDR ID
         */
        @Schema(description = "SDR ID")
        @JsonProperty("sdr_id")
        private Long sdrId;

        /**
         * 设备SN
         */
        @Schema(description = "设备SN")
        private String sn;
    }

    /**
     * 叶子节点
     */
    @Data
    @Schema(description = "叶子节点")
    public static class LeafNode {
        /**
         * 控制源索引
         */
        @Schema(description = "控制源索引")
        @JsonProperty("control_source_index")
        private Integer controlSourceIndex;

        /**
         * SDR ID
         */
        @Schema(description = "SDR ID")
        @JsonProperty("sdr_id")
        private Long sdrId;

        /**
         * 设备SN
         */
        @Schema(description = "设备SN")
        private String sn;

        /**
         * 是否有效
         */
        @Schema(description = "是否有效")
        private Boolean valid;
    }
}
