package org.springblade.modules.beachwaste.controller;

import com.alibaba.excel.EasyExcel;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.excel.GridScheduleImportExcel;
import org.springblade.modules.beachwaste.listener.GridScheduleImportListener;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleMonthQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleRangeDTO;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;
import org.springblade.modules.beachwaste.service.IGridScheduleService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 网格排班信息 控制器
 *
 * <AUTHOR> AI
 */
@RestController
@RequestMapping("/gridSchedule")
@AllArgsConstructor
public class GridScheduleController {

    private final IGridScheduleService gridScheduleService;

    /**
     * 新增 网格排班信息 (处理日期范围)
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "新增(范围)")
    public R saveRange(@Valid @RequestBody GridScheduleRangeDTO gridScheduleRangeDTO) {
        return gridScheduleService.checkAndSaveRange(gridScheduleRangeDTO);
    }

    /**
     * 修改 网格排班信息 (处理日期范围)
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "修改(范围)")
    public R updateRange(@Valid @RequestBody GridScheduleRangeDTO gridScheduleRangeDTO) {
        return gridScheduleService.checkAndUpdateRange(gridScheduleRangeDTO);
    }

    /**
     * 删除 网格排班信息
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(gridScheduleService.removeByIds(List.of(ids.split(","))));
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "详情", description = "传入gridSchedule")
    public R<GridSchedule> detail(GridSchedule gridSchedule) {
        GridSchedule detail = gridScheduleService.getOne(Condition.getQueryWrapper(gridSchedule));
        return R.data(detail);
    }

    /**
     * 列表 网格排班信息
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "列表", description = "传入gridSchedule")
    public R<List<GridSchedule>> list(GridSchedule gridSchedule) {
        List<GridSchedule> list = gridScheduleService.list(Condition.getQueryWrapper(gridSchedule));
        return R.data(list);
    }

    /**
     * 根据网格ID和日期查询排班信息
     */
    @GetMapping("/getByGridAndDate")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "根据网格和日期查询", description = "传入gridId和scheduleDate")
    public R<List<GridSchedule>> getByGridAndDate(@RequestParam(required = false) Long gridId,
                                                  @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate scheduleDate) {
        return R.data(gridScheduleService.getScheduleByGridAndDate(gridId, scheduleDate));
    }

    /**
     * 根据查询条件获取网格排班信息
     */
    @PostMapping("/listByQuery")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "条件查询列表", description = "传入查询参数")
    public R listByQueryDTO(@RequestBody GridScheduleQueryDTO queryDTO) {
        return gridScheduleService.listByQueryDTO(queryDTO);
    }

    /**
     * 获取指定月份网格排班信息
     */
    @PostMapping("/getMonthSchedule")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "获取指定月份网格排班", description = "传入网格ID、年份和月份")
    public R<Map<String, GridSchedule>> getMonthSchedule(@Valid @RequestBody GridScheduleMonthQueryDTO queryDTO) {
        return R.data(gridScheduleService.getMonthScheduleByQuery(queryDTO));
    }

    /**
     * 批量导入网格排班信息
     */
    @PostMapping("/importSchedule")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "批量导入排班信息", description = "传入Excel文件")
    public R importSchedule(@RequestParam("file") MultipartFile file) throws IOException {
        // 创建Excel读取监听器
        GridScheduleImportListener listener = new GridScheduleImportListener();

        // 使用EasyExcel读取上传的Excel文件
        EasyExcel.read(file.getInputStream(), GridScheduleImportExcel.class, listener).sheet().doRead();

        // 获取解析后的数据列表并调用服务进行导入
        List<GridScheduleImportExcel> dataList = listener.getDataList();
        return gridScheduleService.importGridSchedule(dataList);
    }

}
