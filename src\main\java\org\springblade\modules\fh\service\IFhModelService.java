package org.springblade.modules.fh.service;

import org.springblade.modules.fh.pojo.dto.FhModelListRequestDTO;
import org.springblade.modules.fh.pojo.vo.FhModelDetailVO;
import org.springblade.modules.fh.pojo.vo.FhModelVO;

import java.util.List;

/**
 * 模型管理服务接口
 *
 * <AUTHOR> AI
 */
public interface IFhModelService {

    /**
     * 获取项目下的模型列表
     * 该方法调用大疆API获取项目下的模型列表
     *
     * @return 模型列表
     */
    List<FhModelVO> getModelList();

    /**
     * 获取模型详情
     * 该方法调用大疆API获取模型详情
     *
     * @param modelId 模型ID
     * @return 模型详情
     */
	FhModelDetailVO getModelDetail(String modelId);

    /**
     * 获取项目下的模型列表并根据条件过滤
     *
     * @param requestDTO 过滤条件
     * @return 过滤后的模型列表
     */
    List<FhModelVO> getModelList(FhModelListRequestDTO requestDTO);

}
