package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.vo.FhOAPrjDeviceVO;
import org.springblade.modules.fh.pojo.vo.FhWsTokenVO;
import org.springblade.modules.fh.service.IFhOrgService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping( "/fh/org")
@Tag(name = "司空组织项目相关", description = "司空组织项目相关")
public class FhOrgController {
	@Resource
	private IFhOrgService fhOrgService;

	/**
	 * 获取项目设备信息列表
	 *
	 * 该方法通过GET请求访问/prj/devices端点，返回项目设备信息的列表
	 * 主要用于展示项目中所有设备的概览，包括设备的基本信息和状态等
	 *
	 * @return 返回一个包装了项目设备信息列表的响应对象
	 */
	@GetMapping("/prj/devices")
	public R<List<FhOAPrjDeviceVO>> prjDevices(){
		List<FhOAPrjDeviceVO> list = fhOrgService.prjDevices();

		return R.data(list);
	}

	/**
	 * 获取项目Web服务令牌
	 *
	 * 此方法通过GET请求访问/prj/ws-token端点，返回一个FhWsTokenVO对象，
	 * 其中包含了Web服务令牌相关信息此方法委托fhOrgService中的同名方法获取令牌信息，
	 * 并将结果直接返回给调用者
	 *
	 * @return 返回一个R对象，封装了FhWsTokenVO数据，用于传递Web服务令牌信息
	 */
	@GetMapping("/prj/ws-token")
	public R<FhWsTokenVO> prjWsToken(){
	    // 调用服务层方法获取Web服务令牌信息
	    FhWsTokenVO vo = fhOrgService.prjWsToken();

	    // 将获取到的令牌信息封装到响应对象中并返回
	    return R.data(vo);
	}


}
