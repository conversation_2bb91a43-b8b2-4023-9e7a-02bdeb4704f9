package org.springblade.modules.fh.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.component.scheduler.FlightTaskStatusScheduler;
import org.springblade.modules.fh.pojo.dto.*;
import org.springblade.modules.fh.pojo.vo.FhDeviceTaskStatsVO;
import org.springblade.modules.fh.pojo.vo.FhFlightTaskVO;
import org.springblade.modules.fh.pojo.vo.FhMediaResourceVO;
import org.springblade.modules.fh.pojo.vo.FhWaylineVO;
import org.springblade.modules.fh.service.IFhFlightTaskService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 飞行任务管理接口
 *
 * <AUTHOR> AI
 */
@Slf4j
@RestController
@RequestMapping("/fh/flight-task")
@RequiredArgsConstructor
@Tag(name = "飞行任务管理接口")
public class FhFlightTaskController {

    @Resource
    private IFhFlightTaskService flightTaskService;
    @Resource
    private FlightTaskStatusScheduler flightTaskStatusScheduler;

    /**
     * 根据时间获取飞行任务列表
     * 该方法会先获取所有drone设备的SN号，然后循环调用大疆API获取每个设备的飞行任务列表
     *
     * @param requestDTO 请求参数
     * @return 飞行任务列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取飞行任务列表", description = "获取所有设备的飞行任务列表")
    public R<List<FhFlightTaskVO>> getFlightTaskList(@Validated @RequestBody FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        return R.data(flightTaskService.getFlightTaskList(requestDTO));
    }

    /**
     * 根据任务的设定开始时间，统计哪些年份有任务，返回一个年份列表
     *
     * @return 年份列表，按降序排序
     */
    @GetMapping("/years")
    @Operation(summary = "获取任务年份列表", description = "根据任务的设定开始时间，统计哪些年份有任务")
    public R<List<Integer>> getTaskYears() {
        return R.data(flightTaskService.getTaskYears());
    }

    /**
     * 获取设备执行任务统计
     * 根据年份统计每月各设备的任务数量，返回按月份组织的统计结果
     *
     * @param dto 包含年份的请求参数
     * @return 按月份组织的设备任务统计结果列表
     */
    @PostMapping("/device-stats")
    @Operation(summary = "设备任务统计", description = "根据年份统计每月各设备的任务数量")
    public R<List<FhDeviceTaskStatsVO>> getDeviceTaskStats(@Validated @RequestBody FhDeviceTaskStatsDTO dto) {
        return R.data(flightTaskService.getDeviceTaskStats(dto));
    }

    /**
     * 创建飞行任务
     * 该方法调用大疆API创建新的飞行任务
     *
     * @return 创建结果
     */
    @PostMapping(value = "/create")
    public R<String> createFlightTask(@Validated @RequestBody FhFlightTaskCreateDTO createDTO) {
        return flightTaskService.createFlightTask(createDTO);
    }

    /**
     * 获取项目下的航线列表
     * 该方法调用大疆API获取项目下的航线列表
     *
     * @return 航线列表
     */
    @PostMapping("/wayline/list")
    @Operation(summary = "获取航线列表", description = "获取项目下的航线列表")
    public R<List<FhWaylineVO>> getWaylineList() {
        return R.data(flightTaskService.getWaylineList());
    }

    /**
     * 更新飞行任务状态
     * 该方法调用大疆API更新飞行任务的状态
     *
     * @param updateDTO 更新状态请求参数
     * @return 更新结果
     */
    @PostMapping("/status")
    @Operation(description = "更新指定飞行任务的状态")
    public R<String> updateFlightTaskStatus(@Validated @RequestBody FhFlightTaskStatusUpdateDTO updateDTO) {
        return flightTaskService.updateFlightTaskStatus(updateDTO);
    }

    /**
     * 获取飞行任务媒体资源列表
     * 调用大疆API获取指定飞行任务的媒体资源列表
     *
     * @param taskUuid 请求参数
     * @return 媒体资源分页列表
     */
    @GetMapping("/media/list/{taskUuid}")
    @Operation(summary = "获取飞行任务媒体资源列表", description = "获取指定飞行任务的媒体资源列表，支持分页查询")
    public R<List<FhMediaResourceVO>> getFlightTaskMediaList(@Validated @PathVariable String taskUuid) {
        return R.data(flightTaskService.getFlightTaskMediaList(taskUuid));
    }

    /**
     * 根据网格ID和筛选条件获取飞行任务列表
     *
     * @param requestDTO 请求参数，包含网格ID、时间范围、状态和任务类型
     * @return 飞行任务列表
     */
    @PostMapping("/listByGrid")
    @Operation(summary = "根据网格ID和筛选条件获取飞行任务列表", description = "根据网格ID、时间范围、状态和任务类型获取飞行任务列表")
    public R<List<FhFlightTaskVO>> getFlightTaskListByGrid(@Validated @RequestBody FhFlightTaskGridQueryDTO requestDTO) {
        // 在获取列表前先检查并更新飞行任务状态
        flightTaskStatusScheduler.checkAndUpdateFlightTaskStatus();
        return R.data(flightTaskService.getFlightTaskListByGrid(requestDTO));
    }

	/**
	 * 根据ID获取飞行任务详情
	 * @param id 任务id
	 * @return
	 */
	@Deprecated
	@GetMapping("/task/{id}")
	@Operation(summary = "获取飞行任务详情", description = "根据ID获取飞行任务详情")
	public R<FhFlightTaskVO> getFlightTaskById(@PathVariable String id) {
		return R.data(flightTaskService.getFlightTaskById(id));
	}

}
