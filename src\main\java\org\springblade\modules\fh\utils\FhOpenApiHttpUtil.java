package org.springblade.modules.fh.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.ParamCache;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.system.pojo.FhParamEnum;
import org.springblade.modules.system.pojo.entity.Param;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Component
@Slf4j
public class FhOpenApiHttpUtil {
    public static final String X_REQUEST_ID = "X-Request-Id";
    public static final String X_LANGUAGE = "X-Language";
	public static final String X_ORGANIZATION_URL = "X-Organization-Url";
	public static final String X_USER_TOKEN = "X-User-Token";
	public static final String X_PROJECT_UUID = "X-Project-Uuid";

    private FhOpenApiHttpUtil(){}

	public static String getFhUrl(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_URL.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空地址"));
	}

	public static String getFhToken(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_ORG_KEY.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空密钥"));
	}

	public static String getFhOrgUuid(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_ORG_UUID.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空组织uuid"));
	}

	public static String getFhPrjUuid(){
		return Optional.ofNullable(ParamCache.getByKey(FhParamEnum.DJI_FH2_PRJ_UUID.getKey()))
			.map(Param::getParamValue)
			.filter(StrUtil::isNotBlank)
			.orElseThrow(() -> new ServiceException("未配置司空项目uuid"));
	}

	public static String post(String hostUrl,String fhToken,String prjUuid,String body){
		cn.hutool.http.HttpResponse response = HttpRequest.post(hostUrl)
			.header(X_USER_TOKEN, fhToken)
			.header(X_PROJECT_UUID,prjUuid)
			.header(X_REQUEST_ID, UUID.randomUUID().toString())
			.body(body)
			.timeout(60000)
			.execute();
		
		if (response.getStatus() != 200) {
			log.error("openapi请求失败，状态码: {}, 响应内容: {}", response.getStatus(), response.body());
			throw new ServiceException("openapi请求失败，状态码: " + response.getStatus());
		}
		return response.body();
	}
    public static String post(String host,String fhToken,String prjUuid,String url,String body){
        return post(host + url,fhToken,prjUuid,body);
    }
    public static String post(String url,String body){
        return post(getFhUrl(),getFhToken(),getFhPrjUuid(),url,body);
    }

	public static String get(String hostUrl,String fhToken,String prjUuid){
		cn.hutool.http.HttpResponse response = HttpRequest.get(hostUrl)
			.header(X_USER_TOKEN, fhToken)
			.header(X_PROJECT_UUID,prjUuid)
			.header(X_REQUEST_ID, UUID.randomUUID().toString())
			.timeout(60000)
			.execute();
		
		if (response.getStatus() != 200) {
			log.error("openapi请求失败，状态码: {}, 响应内容: {}", response.getStatus(), response.body());
			throw new ServiceException("openapi请求失败，状态码: " + response.getStatus());
		}
		return response.body();
	}
    public static String get(String host,String fhToken,String prjUuid,String url){
        return get(host + url,fhToken,prjUuid);
    }
    public static String get(String url){
        return get(getFhUrl(),getFhToken(),getFhPrjUuid(),url);
    }

	public static String delete(String hostUrl,String fhToken,String prjUuid,String body){
		cn.hutool.http.HttpResponse response = HttpRequest.delete(hostUrl)
			.header(X_USER_TOKEN, fhToken)
			.header(X_PROJECT_UUID, prjUuid)
			.header(X_REQUEST_ID, UUID.randomUUID().toString())
			.body(body)
			.timeout(60000)
			.execute();
		
		if (response.getStatus() != 200) {
			log.error("openapi请求失败，状态码: {}, 响应内容: {}", response.getStatus(), response.body());
			throw new ServiceException("openapi请求失败，状态码: " + response.getStatus());
		}
		return response.body();
	}
	public static String delete(String host,String fhToken,String prjUuid,String url,String body){
		return delete(host + url,fhToken,prjUuid,body);
	}
    public static String delete(String url,String body){
        return delete(getFhUrl(),getFhToken(),getFhPrjUuid(),url,body);
    }

	public static String put(String hostUrl,String fhToken,String prjUuid,String body){
		cn.hutool.http.HttpResponse response = HttpRequest.put(hostUrl)
			.header(X_USER_TOKEN, fhToken)
			.header(X_PROJECT_UUID,prjUuid)
			.header(X_REQUEST_ID, UUID.randomUUID().toString())
			.body(body)
			.timeout(60000)
			.execute();
		
		if (response.getStatus() != 200) {
			log.error("openapi请求失败，状态码: {}, 响应内容: {}", response.getStatus(), response.body());
			throw new ServiceException("openapi请求失败，状态码: " + response.getStatus());
		}
		return response.body();
	}
    public static String put(String host,String fhToken,String prjUuid,String url,String body){
        return put(host + url,fhToken,prjUuid,body);
    }
    public static String put(String url,String body){
        return put(getFhUrl(),getFhToken(),getFhPrjUuid(),url,body);
    }

    public static JSONObject resStrToJson(String response) {
        if (StrUtil.isEmpty(response)) {
            throw new RuntimeException("openapi响应数据异常");
        }

        log.info("openapi http响应数据 --> " + response);

        // 预处理响应数据，移除可能的BOM标记和特殊字符
        response = response.trim();
        if (response.startsWith("\uFEFF")) {
            response = response.substring(1);
        }

        // 校验JSON格式
        if (!response.startsWith("{")) {
            log.error("响应数据不是有效的JSON对象格式: {}", response);
            throw new RuntimeException("响应数据格式错误，不是有效的JSON对象");
        }

        try {
            // 使用JSONUtil解析JSON字符串
            JSONObject obj = JSONUtil.parseObj(response);

            // 验证必要字段
            if (!obj.containsKey("code")) {
                throw new RuntimeException("响应数据缺少code字段");
            }

            Integer code = obj.getInt("code");
            if (!Objects.equals(0, code)) {
                String message = obj.getStr("message", "未知错误");
                throw new RuntimeException(message);
            }

            return obj;
        } catch (RuntimeException r) {
            log.error("处理响应数据时发生运行时异常: {}", r.getMessage());
            throw r;
        } catch (Exception e) {
            log.error("解析响应数据时发生异常", e);
            throw new RuntimeException("openapi响应数据解析异常: " + e.getMessage());
        }
    }

    public static JSONObject resData(String response){
        JSONObject obj = resStrToJson(response);
        return obj.getJSONObject("data");
    }
}
