package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * 网格事件查询返回VO
 * <AUTHOR>
@Data
public class GridEventVO {

    /**
     * 事件ID
     */
    private Long id;

    /**
     * 事件标题
     */
    private String title;

    /**
     * 事件状态
     */
    private String eventStatus;

    /**
     * 垃圾尺寸
     */
    private String wasteSize;

    /**
     * 网格管理员名称
     */
    private String gridManagerName;

    /**
     * 事件发现时间（格式：yyyy/MM/dd）
     */
    private Date discoveryTime;

    /**
     * 处理后现场照片存储路径
     */
    private String processedImagePath;

    /**
     * 发现方式
     */
    private String discoveryMethod;

    /**
     * 置信度
     */
    private String confidence;

}
