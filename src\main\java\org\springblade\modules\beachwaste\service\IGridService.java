package org.springblade.modules.beachwaste.service;

import org.springblade.modules.beachwaste.pojo.dto.GridFlightTaskQueryDTO;
import org.springblade.modules.beachwaste.pojo.vo.GridStatisticsVo;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;

import java.util.List;
import java.util.Map;

public interface IGridService {

    /**
     * 获取网格统计数据
     *
     * @return 网格统计数据视图对象
     */
    GridStatisticsVo getGridStatistics();
    

    /**
     * 根据年份、月份和网格ID获取飞行任务数据
     *
     * @param queryDTO 包含年份、月份和网格ID的查询参数
     * @return 按月份分组的飞行任务列表，key为月份（格式：yyyy-MM），value为该月的飞行任务列表
     */
    Map<String, List<FhFlightTask>> getGridFlightTasksByYearMonth(GridFlightTaskQueryDTO queryDTO);

}
