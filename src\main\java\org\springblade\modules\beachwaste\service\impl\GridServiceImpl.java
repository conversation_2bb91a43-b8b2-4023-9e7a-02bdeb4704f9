package org.springblade.modules.beachwaste.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.beachwaste.mapper.EventMapper;
import org.springblade.modules.beachwaste.mapper.SpatialGridMapper;
import org.springblade.modules.beachwaste.pojo.dto.GridFlightTaskQueryDTO;
import org.springblade.modules.beachwaste.pojo.vo.GridStatisticsVo;
import org.springblade.modules.beachwaste.service.IGridService;
import org.springblade.modules.fh.mapper.FhFlightTaskMapper;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class GridServiceImpl implements IGridService {

    private final SpatialGridMapper gridMapper;
    private final EventMapper eventMapper;
    private final IRoleService roleService;
    private final IUserService userService;
    private final FhFlightTaskMapper flightTaskMapper;

    /**
     * 获取网格统计数据
     * @return 网格统计数据VO对象
     */
	@Override
    public GridStatisticsVo getGridStatistics() {
        // 获取网格基础统计
        Map<String, Object> gridStats = gridMapper.getGridBaseStats();

        // 获取事件统计
        Map<String, Object> eventStats = eventMapper.getEventStats();

        // 获取当前租户ID
        String tenantId = AuthUtil.getTenantId();
        // 获取网格员角色ID
        String gridStaffRoleIds = roleService.getRoleIds(tenantId, "网格员");
        int staffCount = 0;
        if (gridStaffRoleIds != null && !gridStaffRoleIds.isEmpty()) {
            List<String> roleIdList = Arrays.stream(gridStaffRoleIds.split(",")).collect(Collectors.toList());
			LambdaQueryWrapper<User> eq = new LambdaQueryWrapper<User>()
				.in(User::getRoleId, roleIdList)
				.eq(User::getTenantId, tenantId)
				.eq(User::getIsDeleted, 0);
			staffCount = userService.getBaseMapper().selectList(eq).size();
        }

        // 组装结果
        GridStatisticsVo result = new GridStatisticsVo();
        result.setGridCount(((Number) gridStats.get("grid_count")).intValue());
        result.setTotalArea(new BigDecimal(gridStats.get("total_area").toString()).setScale(2, RoundingMode.HALF_UP));
        result.setTotalEvents(((Number) eventStats.get("total_events")).intValue());
        result.setProcessedEvents(((Number) eventStats.get("processed_events")).intValue());

        // 处理平均处理时长可能为null的情况
        Object avgProcessTime = eventStats.get("avg_process_time");
        result.setAvgProcessTime(avgProcessTime != null ? new BigDecimal(avgProcessTime.toString()) : BigDecimal.ZERO);

        result.setGridStaffCount(staffCount);

        // 获取总飞行时长
        BigDecimal totalFlightTime = flightTaskMapper.getTotalFlightTime();
        result.setTotalFlightTime(totalFlightTime);

        // 获取完成任务次数（只统计终止和成功状态）
        LambdaQueryWrapper<FhFlightTask> taskWrapper = new LambdaQueryWrapper<FhFlightTask>()
            .in(FhFlightTask::getTaskStatus, Arrays.asList("success", "stopped"))
            .eq(FhFlightTask::getIsDeleted, 0);
        Long completedTaskCount = flightTaskMapper.selectCount(taskWrapper);
        result.setCompletedTaskCount(completedTaskCount);

        return result;
    }

    /**
     * 根据年份、月份和网格ID获取飞行任务数据
     * 当某日没有飞行任务数据时也需要返回
     * 按月查询时会返回前后各一个月的数据（例如查询4月，返回3月1日到5月31日的数据）
     *
     * @param queryDTO 包含年份、月份和网格ID的查询参数
     * @return 日期-飞行任务列表映射，包含指定日期范围内所有日期
     */
    @Override
    public Map<String, List<FhFlightTask>> getGridFlightTasksByYearMonth(GridFlightTaskQueryDTO queryDTO) {
        // 获取指定年份
        int year = queryDTO.getYear();

        // 判断是否按年查询
        boolean isYearQuery = queryDTO.getMonth() == null;

        // 计算查询范围的第一天和最后一天
        LocalDate startDate;
        LocalDate endDate;
        if (isYearQuery) {
            // 按年查询
            startDate = Year.of(year).atDay(1);
            endDate = Year.of(year).atMonthDay(MonthDay.of(12, 31));
        } else {
            // 按月查询 - 返回前后各一个月的数据
            YearMonth targetMonth = YearMonth.of(year, queryDTO.getMonth());
            // 上个月的第一天
            startDate = targetMonth.minusMonths(1).atDay(1);
            // 下个月的最后一天
            endDate = targetMonth.plusMonths(1).atEndOfMonth();
        }

        // 转换为OffsetDateTime用于查询
        OffsetDateTime startDateTime = startDate.atStartOfDay().atZone(ZoneId.systemDefault()).toOffsetDateTime();
        OffsetDateTime endDateTime = endDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toOffsetDateTime();

        // 查询指定范围该网格的所有飞行任务记录
        LambdaQueryWrapper<FhFlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(FhFlightTask::getIsDeleted, 0)
            .between(FhFlightTask::getBeginAt, startDateTime, endDateTime)
            .orderByAsc(FhFlightTask::getBeginAt);

        List<FhFlightTask> taskList = flightTaskMapper.selectList(queryWrapper);
        String gridIdStr = queryDTO.getGridId().toString();

        // 过滤出与指定网格相关的任务
        List<FhFlightTask> filteredTaskList = taskList.stream().filter(task -> {
            if (task.getGridIds() == null || task.getGridIds().isEmpty()) {
                return false;
            }
            String[] ids = task.getGridIds().split(",");
            for (String id : ids) {
                if (gridIdStr.equals(id.trim())) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());

        // 构建返回结果，确保每一天都有记录
        Map<String, List<FhFlightTask>> resultMap = new LinkedHashMap<>();

        // 使用流处理，为指定范围每一天创建一个键值对
        Stream.iterate(startDate, date -> date.plusDays(1))
            .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
            .forEach(date -> {
                // 格式为: yyyy-MM-dd
                String dateStr = date.toString();

                // 查找该日期的飞行任务
                List<FhFlightTask> dayTasks = filteredTaskList.stream()
                    .filter(task -> {
                        // 将OffsetDateTime转换为LocalDate进行比较
                        LocalDate taskDate = task.getBeginAt().toLocalDate();
                        return taskDate.equals(date);
                    })
                    .collect(Collectors.toList());

                // 即使没有任务，也添加空列表
                resultMap.put(dateStr, dayTasks);
            });

        return resultMap;
    }

}
