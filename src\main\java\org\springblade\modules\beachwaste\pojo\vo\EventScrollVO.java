package org.springblade.modules.beachwaste.pojo.vo;


import lombok.Data;

import java.util.Date;

/**
 * 事件滚动列表VO
 *
 * <AUTHOR>
@Data
public class EventScrollVO {

    /**
     * 发现时间（格式：yyyy/MM/dd）
     */
    private Date discoveryTime;

    /**
     * 垃圾材质与尺寸
     */
    private String wasteInfo;

    /**
     * 发现方式
     */
    private String discoveryMethod;

	/**
     * 处理状态
     */
    private String eventStatus;

}
