package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.vo.FhDeviceStateVO;
import org.springblade.modules.fh.service.IFhDeviceService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备管理接口
 *
 * <AUTHOR> AI
 */
@Slf4j
@RestController
@RequestMapping("/fh/device")
@RequiredArgsConstructor
@Tag(name = "设备管理接口")
public class FhDeviceController {

    @Resource
    private IFhDeviceService deviceService;

    /**
     * 获取设备物模型状态
     * 该方法调用大疆API获取设备的物模型状态
     * 关于设备物模模型属性的更多细节，请查阅上云API中的设备属性文档
     *
     * @param sn 设备SN
     * @return 设备物模型状态
     */
    @GetMapping("/{sn}/state")
    @Operation(summary = "获取设备物模型状态", description = "获取指定设备的物模型状态，包含设备模型信息和物模型数据")
    public R<FhDeviceStateVO> getDeviceState(@Parameter(description = "设备SN", required = true) @PathVariable String sn) {
        log.info("获取设备物模型状态, sn: {}", sn);
        return R.data(deviceService.getDeviceState(sn));
    }
}
