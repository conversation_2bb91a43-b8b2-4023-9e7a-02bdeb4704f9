package org.springblade.modules.fh.mqtt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.constant.ChannelName;
import org.springblade.modules.fh.utils.SpringBeanUtils;
import org.springframework.integration.annotation.Router;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.router.AbstractMessageRouter;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;

@Component
public class InboundMessageRouter extends AbstractMessageRouter {

    private static final Logger log = LoggerFactory.getLogger(InboundMessageRouter.class);

    /**
     * All mqtt broker messages will arrive here before distributing them to different channels.
     * 所有mqtt代理消息在分发到不同通道之前都会到达这里。
     * @param message message from mqtt broker
     * @return channel
     */
    @Override
    @Router(inputChannel = ChannelName.INBOUND)
    protected Collection<MessageChannel> determineTargetChannels(Message<?> message) {
        MessageHeaders headers = message.getHeaders();
        String topic = headers.get(MqttHeaders.RECEIVED_TOPIC).toString();
        byte[] payload = (byte[])message.getPayload();

		//log.debug("收到 topic: {} \t payload =>{}", topic, new String(payload));

		FhApiTopicEnum topicEnum = FhApiTopicEnum.find(topic);
        MessageChannel bean = (MessageChannel) SpringBeanUtils.getBean(topicEnum.getBeanName());

        return Collections.singleton(bean);
    }
}
