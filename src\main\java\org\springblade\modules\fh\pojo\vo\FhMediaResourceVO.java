package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 媒体资源VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "媒体资源信息")
public class FhMediaResourceVO {

    @Schema(description = "媒体资源ID")
    private String uuid;

	@Schema(description = "媒体资源名称")
	private String name;

    @Schema(description = "媒体文件的后缀")
    private String file_type;

    @Schema(description = "媒体文件的后缀")
    private String suffix;

    @Schema(description = "媒体文件大小(字节)")
    private Integer size;

    @Schema(description = "预览图片链接")
    private String preview_url;

    @Schema(description = "原图下载链接")
    private String original_url;

    @Schema(description = "媒体文件创建时间")
    private String create_at;

    @Schema(description = "媒体文件创建时间")
    private String update_at;

}
