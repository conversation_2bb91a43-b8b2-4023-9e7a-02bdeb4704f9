package org.springblade.modules.fh.pojo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springblade.modules.fh.enums.FlightTaskType;

/**
 * 获取飞行任务列表请求 DTO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "获取飞行任务列表请求参数")
public class FhFlightTaskRequestByTimeBaseDTO {

    @Schema(description = "设备SN", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("sn")
    private String deviceSn;

    @Schema(description = "任务名称，非必填")
    private String name;

    @Schema(description = "开始时间(秒级时间戳)", requiredMode = Schema.RequiredMode.REQUIRED)
	@NotNull(message = "开始时间不能为空")
    private Long beginAt;

    @Schema(description = "结束时间(秒级时间戳)", requiredMode = Schema.RequiredMode.REQUIRED)
	@NotNull(message = "结束时间不能为空")
    private Long endAt;

    @Schema(description = "任务类型：immediate(立即任务)，timed(单次定时任务)，recurring(重复任务)，continuous(连续任务)，非必填，不填写则为全部类型")
    private FlightTaskType taskType;

	/**
	 * 任务状态数组：
	 * waiting(待开始)
	 * starting_failure(启动失败)
	 * executing(执行中)
	 * paused(暂停)
	 * terminated(终止)
	 * success(成功)
	 * suspended(挂起)
	 * timeout(超时)
	 * 非必填，不填写则为全部状态
	 */
    private String[] status;

    @Schema(description = "网格ID，非必填")
    private String gridId;

}
