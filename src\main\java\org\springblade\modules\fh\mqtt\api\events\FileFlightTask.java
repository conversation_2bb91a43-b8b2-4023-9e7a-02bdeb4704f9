package org.springblade.modules.fh.mqtt.api.events;

public class FileFlightTask {

    private Integer expected_file_count;
    private Integer flight_type;
    private Integer uploaded_file_count;

    public FileFlightTask() {
    }

    public Integer getExpected_file_count() {
        return expected_file_count;
    }

    public void setExpected_file_count(Integer expected_file_count) {
        this.expected_file_count = expected_file_count;
    }

    public Integer getFlight_type() {
        return flight_type;
    }

    public void setFlight_type(Integer flight_type) {
        this.flight_type = flight_type;
    }

    public Integer getUploaded_file_count() {
        return uploaded_file_count;
    }

    public void setUploaded_file_count(Integer uploaded_file_count) {
        this.uploaded_file_count = uploaded_file_count;
    }

    @Override
    public String toString() {
        return "FileFlightTask{" +
                "expected_file_count=" + expected_file_count +
                ", flight_type=" + flight_type +
                ", uploaded_file_count=" + uploaded_file_count +
                '}';
    }
}
