package org.springblade.modules.fh.mqtt.handler.events;


import com.fasterxml.jackson.core.type.TypeReference;
import org.springblade.common.constant.ChannelName;
import org.springblade.modules.fh.mqtt.api.events.FileUploadCallback;

import java.util.Arrays;

public enum EventsMethodEnum {

//    FLIGHTTASK_PROGRESS("flighttask_progress", ChannelName.INBOUND_EVENTS_FLIGHTTASK_PROGRESS, new TypeReference<EventsDataRequest<FlighttaskProgress>>() {}),

    FILE_UPLOAD_CALLBACK("file_upload_callback", ChannelName.INBOUND_EVENTS_FILE_UPLOAD_CALLBACK, new TypeReference<FileUploadCallback>() {}),

    UNKNOWN("", ChannelName.DEFAULT, new TypeReference<>() {});

    private final String method;

    private final String channelName;

    private final TypeReference classType;

    EventsMethodEnum(String method, String channelName, TypeReference classType) {
        this.method = method;
        this.channelName = channelName;
        this.classType = classType;
    }

    public String getMethod() {
        return method;
    }

    public String getChannelName() {
        return channelName;
    }

    public TypeReference getClassType() {
        return classType;
    }

    public static EventsMethodEnum find(String method) {
        return Arrays.stream(EventsMethodEnum.values())
                .filter(methodEnum -> methodEnum.method.equals(method))
                .findAny().orElse(UNKNOWN);
    }
}
