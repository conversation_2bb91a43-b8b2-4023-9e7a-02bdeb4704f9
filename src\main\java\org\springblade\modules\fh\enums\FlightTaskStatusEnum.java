package org.springblade.modules.fh.enums;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 飞行任务状态枚举类
 */
@Schema(description = "飞行任务状态枚举")
public enum FlightTaskStatusEnum {

	@Schema(description = "待开始")
	WAITING("waiting", "待开始"),

	@Schema(description = "启动失败")
	STARTING_FAILURE("starting_failure", "启动失败"),

	@Schema(description = "执行中")
	EXECUTING("executing", "执行中"),

	@Schema(description = "暂停")
	PAUSED("paused", "暂停"),

	@Schema(description = "终止")
	TERMINATED("terminated", "终止"),

	@Schema(description = "成功")
	SUCCESS("success", "成功"),

	@Schema(description = "挂起")
	SUSPENDED("suspended", "挂起"),

	@Schema(description = "超时")
	TIMEOUT("timeout", "超时");

	private final String value;
	private final String description;

	FlightTaskStatusEnum(String value, String description) {
		this.value = value;
		this.description = description;
	}

	public String getValue() {
		return value;
	}

	public String getDescription() {
		return description;
	}

	/**
	 * 根据值获取描述信息
	 *
	 * @param value 状态值
	 * @return 描述信息
	 */
	public static String getDescriptionByValue(String value) {
		for (FlightTaskStatusEnum status : FlightTaskStatusEnum.values()) {
			if (status.getValue().equals(value)) {
				return status.getDescription();
			}
		}
		return null;
	}
}
