package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.dto.FhModelListRequestDTO;
import org.springblade.modules.fh.pojo.vo.FhModelDetailVO;
import org.springblade.modules.fh.pojo.vo.FhModelVO;
import org.springblade.modules.fh.service.IFhModelService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
// import java.util.stream.Collectors; // 移除不再需要的导入

/**
 * 模型管理接口
 *
 * <AUTHOR> AI
 */
@Slf4j
@RestController
@RequestMapping("/fh/model")
@RequiredArgsConstructor
@Tag(name = "模型管理接口")
public class FhModelController {

    @Resource
    private IFhModelService modelService;

    /**
     * 获取项目下的模型列表
     * 该方法调用大疆API获取项目下的模型列表
     *
     * @return 模型列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取模型列表", description = "获取项目下的模型列表")
    public R<List<FhModelVO>> getModelList(@RequestBody FhModelListRequestDTO requestDTO) {
        // 调用 Service 层新添加的带过滤参数的方法
        List<FhModelVO> filteredList = modelService.getModelList(requestDTO);

        return R.data(filteredList);
    }

    /**
     * 获取模型详情
     * 该方法调用大疆API获取模型详情
     *
     * @param modelId 模型ID
     * @return 模型详情
     */
    @GetMapping("/detail/{modelId}")
    @Operation(summary = "获取模型详情", description = "获取模型详情信息")
    public R<FhModelDetailVO> getModelDetail(@PathVariable("modelId") String modelId) {
        return R.data(modelService.getModelDetail(modelId));
    }
}
