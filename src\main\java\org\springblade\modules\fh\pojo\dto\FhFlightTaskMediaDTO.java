package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 获取飞行任务媒体资源请求参数
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "获取飞行任务媒体资源请求参数")
public class FhFlightTaskMediaDTO {

    @NotBlank(message = "任务ID不能为空")
    @Schema(description = "飞行任务ID")
    private String taskId;

    @Schema(description = "媒体资源类型，可选值：photo(照片), video(视频), all(全部)", defaultValue = "all")
    private String mediaType = "all";

    @Schema(description = "分页页码，从1开始", defaultValue = "1")
    private Integer page = 1;

    @Schema(description = "每页数量，默认10，最大100", defaultValue = "10")
    private Integer pageSize = 10;
}
