package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;

import java.time.LocalDate;
import java.util.List;

/**
 * 网格排班范围数据传输对象
 *
 * <AUTHOR> AI
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "网格排班范围数据传输对象")
public class GridScheduleRangeDTO extends GridSchedule {

    @Schema(description = "时间范围列表")
    private List<TimeRange> timeRanges;

    @Schema(description = "开始日期")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    private LocalDate endDate;

    @Data
    @Schema(description = "时间范围")
    public static class TimeRange {
        @Schema(description = "开始日期")
        private LocalDate startDate;

        @Schema(description = "结束日期")
        private LocalDate endDate;
    }

}
