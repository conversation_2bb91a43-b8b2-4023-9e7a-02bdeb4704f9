package org.springblade.modules.fh.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.modules.fh.pojo.vo.FhModelDetailVO;
import org.springblade.modules.fh.pojo.vo.FhModelVO;
import org.springblade.modules.fh.service.IFhModelService;
import org.springblade.modules.fh.service.IFhOrgService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 模型管理服务实现类
 *
 * <AUTHOR> AI
 */
@Slf4j
@Service
public class FhModelServiceImpl implements IFhModelService {

    @Resource
    private IFhOrgService fhOrgService;

    /**
     * 获取项目下的模型列表
     * 该方法调用大疆API获取项目下的模型列表
     *
     * @return 模型列表
     */
    @Override
    public List<FhModelVO> getModelList() {
        try {
            // 调用大疆API获取模型列表
            String result = FhOpenApiHttpUtil.get(FhOpenapiPathConstant.MODELS);
            log.info("获取模型列表结果: {}", result);

            // 使用工具类处理响应数据
            JSONObject data = FhOpenApiHttpUtil.resData(result);

            // 返回模型列表
            return data.getBeanList("list", FhModelVO.class);
        } catch (Exception e) {
            log.error("获取模型列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取模型详情
     * 该方法调用大疆API获取模型详情
     *
     * @param modelId 模型ID
     * @return 模型详情
     */
    @Override
    public FhModelDetailVO getModelDetail(String modelId) {
        try {
            // 构建请求路径
            String path = String.format(FhOpenapiPathConstant.MODEL_DETAIL, modelId);

            // 调用大疆API获取模型详情
            String result = FhOpenApiHttpUtil.get(path);
            log.info("获取模型详情结果: {}", result);

            // 使用工具类处理响应数据
            JSONObject data = FhOpenApiHttpUtil.resData(result);

            // 返回模型详情
            return JSONUtil.toBean(data, FhModelDetailVO.class);
        } catch (Exception e) {
            log.error("获取模型详情异常", e);
            return null;
        }
    }

    /**
     * 获取项目下的模型列表并根据条件过滤
     *
     * @param requestDTO 过滤条件
     * @return 过滤后的模型列表
     */
    @Override
    public List<FhModelVO> getModelList(org.springblade.modules.fh.pojo.dto.FhModelListRequestDTO requestDTO) {
        List<FhModelVO> modelList = getModelList();

        // 根据时间范围和文件类型过滤列表
        List<FhModelVO> filteredList = modelList.stream()
                .filter(model -> {
                    boolean timeFilter = true;
                    if (requestDTO.getStartTime() != null && model.getCreateAt() < requestDTO.getStartTime()) {
                        timeFilter = false;
                    }
                    if (requestDTO.getEndTime() != null && model.getCreateAt() > requestDTO.getEndTime()) {
                        timeFilter = false;
                    }
                    boolean fileTypeFilter = true;
                    if (requestDTO.getFileType() != null && !requestDTO.getFileType().isEmpty() && !requestDTO.getFileType().equals(model.getFileType())) {
                        fileTypeFilter = false;
                    }
                    return timeFilter && fileTypeFilter;
                })
                .collect(java.util.stream.Collectors.toList());

        return filteredList;
    }

}
