package org.springblade.modules.fh.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.fh.pojo.vo.DeviceModelVO;
import org.springblade.modules.fh.pojo.vo.DeviceStateVO;
import org.springblade.modules.fh.pojo.vo.FhDeviceStateVO;
import org.springblade.modules.fh.service.IFhDeviceService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.stereotype.Service;

/**
 * 设备管理服务实现类
 *
 * <AUTHOR> AI
 */
@Slf4j
@Service
public class FhDeviceServiceImpl implements IFhDeviceService {

    /**
     * 获取设备物模型状态
     * 该方法调用大疆API获取设备的物模型状态
     *
     * @param sn 设备SN
     * @return 设备物模型状态
     */
    @Override
    public FhDeviceStateVO getDeviceState(String sn) {
        try {
            // 构建请求路径
            String path = String.format(FhOpenapiPathConstant.DEVICE_STATE, sn);

            // 调用大疆API获取设备物模型状态
            String result = FhOpenApiHttpUtil.get(path);
            log.info("获取设备物模型状态结果: {}", result);

            // 使用工具类处理响应数据
            JSONObject data = FhOpenApiHttpUtil.resData(result);

            // 创建返回对象
            FhDeviceStateVO deviceStateVO = new FhDeviceStateVO();

            // 设置设备SN
            deviceStateVO.setDeviceSn(sn);

            // 设置设备模型
            if (data.containsKey("device_model")) {
                DeviceModelVO deviceModel = JSONUtil.toBean(data.getJSONObject("device_model"), DeviceModelVO.class);
                deviceStateVO.setDeviceModel(deviceModel);
            }

            // 设置物模型状态
            if (data.containsKey("device_state")) {
                JSONObject stateJson = data.getJSONObject("device_state");
                DeviceStateVO deviceState = JSONUtil.toBean(stateJson, DeviceStateVO.class);
                deviceStateVO.setDeviceState(deviceState);
            }

            return deviceStateVO;
        } catch (Exception e) {
            log.error("获取设备物模型状态异常", e);
            throw new ServiceException("获取设备物模型状态失败: " + e.getMessage());
        }
    }
}
