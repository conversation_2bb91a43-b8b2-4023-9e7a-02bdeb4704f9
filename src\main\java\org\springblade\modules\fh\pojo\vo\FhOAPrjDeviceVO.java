package org.springblade.modules.fh.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "DjiFhOADeviceVO对象")
public class FhOAPrjDeviceVO implements Serializable {
    @Serial
	private static final long serialVersionUID = 1L;

    private FhOADeviceVO gateway;
    private FhOADeviceVO drone;
}
