package org.springblade.modules.beachwaste.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.enums.EventStatusEnum;
import org.springblade.modules.beachwaste.mapper.EventMapper;
import org.springblade.modules.beachwaste.mapper.SpatialGridMapper;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridDetailVo;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridInfoVo;
import org.springblade.modules.beachwaste.pojo.vo.SpatialGridListVo;
import org.springblade.modules.beachwaste.service.IGridScheduleService;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.beachwaste.util.GeometryUtil;
import org.springblade.modules.beachwaste.util.KmlParserUtil;
import org.springblade.modules.beachwaste.util.ValidationUtil;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 空间网格服务实现类
 *
 * <AUTHOR>
@Slf4j
@Service
@AllArgsConstructor
public class SpatialGridServiceImpl extends ServiceImpl<SpatialGridMapper, SpatialGrid> implements ISpatialGridService {

	private IUserService userService;
	private EventMapper eventMapper;
	private IGridScheduleService gridScheduleService;

	@Override
	public R checkToSave(SpatialGrid spatialGrid) {
		// 调用工具类方法
		if (ValidationUtil.checkBase(spatialGrid.getUserId(), spatialGrid.getGridCode(), spatialGrid.getGridName())) {
			return R.fail("网格数据只能关联管理员角色");
		}
		return R.status(this.save(spatialGrid));
	}

	@Override
	public R checkToUpdateById(SpatialGrid spatialGrid) {
		if (ValidationUtil.checkBaseForUpdate(spatialGrid.getId(), spatialGrid.getUserId(), spatialGrid.getGridCode(), spatialGrid.getGridName())) {
			return R.fail("网格数据只能关联管理员角色");
		}
		return R.status(this.updateById(spatialGrid));
	}

	@Override
	public R getGridDetailWithEvents(Long id, String yearMonth) {
		SpatialGrid grid = this.getById(id);
		if (grid == null) {
			return R.fail("网格不存在");
		}

		// 获取管理员联系方式
		User inspector = userService.getById(grid.getUserId());
		if (inspector == null) {
			return R.fail("管理员信息不存在");
		}

		// 查询本月已处理的事件
		LambdaQueryWrapper<Event> wq = new LambdaQueryWrapper<>();
		wq.eq(Event::getGridId, id);
		wq.eq(Event::getStatus, EventStatusEnum.PROCESSED.getId());

		// 使用Java日期API计算月份的开始和结束 LocalDateTime
		YearMonth ym = YearMonth.parse(yearMonth);
		LocalDateTime startDateTime = ym.atDay(1).atStartOfDay();
		LocalDateTime endDateTime = ym.atEndOfMonth().atTime(LocalTime.MAX);

		// 直接使用 LocalDateTime 对象进行比较
		wq.between(Event::getDiscoveryTime, startDateTime, endDateTime);

		// 使用 eventMapper 替代 eventService
		long count = eventMapper.selectCount(wq);

		// 使用VO封装返回结果
		SpatialGridDetailVo detailVo = new SpatialGridDetailVo();
		detailVo.setGrid(grid);
		detailVo.setPhoneNumber(inspector.getPhone());
		detailVo.setProcessedCount(count);

		return R.data(detailVo);
	}

	/**
	 * 解析KML文件内容并保存坐标数据
	 *
	 * @param file KML格式的上传文件
	 */
	@Override
	public R parseKmlFile(MultipartFile file, Long id, String gridName, String gridCode) {
		// 调用工具类方法
		if (ValidationUtil.checkBase(id, gridCode, gridName)) {
			return R.fail("网格数据只能关联管理员角色");
		}

		SpatialGrid spatialGrid = new SpatialGrid();
		spatialGrid.setGridCode(gridCode);
		spatialGrid.setGridName(gridName);

		try {
			// 创建DOM解析器
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = factory.newDocumentBuilder();
			Document doc = builder.parse(file.getInputStream());
			doc.getDocumentElement().normalize();

			// 校验Placemark标签数量
			int placemarkCount = KmlParserUtil.countPlacemarks(doc);
			if (placemarkCount > 1) {
				return R.fail("上传KML文件只能包含单个地理要素，当前检测到" + placemarkCount + "个地理要素");
			}

			// 获取KML文件中的GEOM类型
			String geomType = KmlParserUtil.getGeomType(doc);
			spatialGrid.setGeomType(geomType);

			// 解析坐标数据
			String[] coordinates = KmlParserUtil.parseCoordinates(doc);

			// 处理几何数据
			processGeometryData(spatialGrid, geomType, coordinates);

			// 存储userId
			spatialGrid.setUserId(id);

			boolean save = this.save(spatialGrid);
			if (save) {
				return R.success("网格数据保存成功");
			} else {
				return R.fail("网格数据保存失败");
			}

		} catch (Exception e) {
			e.printStackTrace();
			return R.fail("网格导入失败：" + e.getMessage());
		}
	}

	/**
	 * 解析KML文件中的多个地理要素并返回解析结果
	 *
	 * @param file 上传的KML文件对象
	 * @return 解析结果（包含多个地理要素信息的列表）
	 */
	@Override
	public R parseMultipleFeatures(MultipartFile file) {
		try {
			// 创建DOM解析器
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = factory.newDocumentBuilder();
			Document doc = builder.parse(file.getInputStream());
			doc.getDocumentElement().normalize();

			// 获取KML文件中的所有地理要素
			List<SpatialGridInfoVo> features = KmlParserUtil.parseMultipleFeatures(doc);

			// 如果没有解析到任何地理要素，返回错误信息
			if (features.isEmpty()) {
				return R.fail("未在KML文件中检测到有效的地理要素");
			}

			// 为每个地理要素计算WKT、GeoJSON和面积
			for (SpatialGridInfoVo feature : features) {
				String geomType = feature.getGeomType();
				String[] coordinates = feature.getCoordinates();

				// 处理几何数据
				processGeometryData(feature, geomType, coordinates);
			}

			return R.data(features);

		} catch (Exception e) {
			e.printStackTrace();
			return R.fail("KML解析失败：" + e.getMessage());
		}
	}

	/**
	 * 处理几何数据（WKT、GeoJSON和面积计算）
	 *
	 * @param entity      需要设置几何数据的实体对象（SpatialGrid或SpatialGridInfo）
	 * @param geomType    几何类型
	 * @param coordinates 坐标数组
	 */
	private void processGeometryData(Object entity, String geomType, String[] coordinates) {
		// 构造WKT格式数据
		String wktStr = GeometryUtil.createWkt(geomType, coordinates);

		// 构造GeoJSON格式数据
		String geoJsonStr = GeometryUtil.createGeoJson(geomType, coordinates);

		// 计算面积
		double area = GeometryUtil.calculateArea(geomType, coordinates);

		// 根据实体类型设置属性
		if (entity instanceof SpatialGrid) {
			SpatialGrid grid = (SpatialGrid) entity;
			grid.setGridGeom(wktStr);
			grid.setGeomJson(geoJsonStr);
			grid.setGridArea(BigDecimal.valueOf(area));
		} else if (entity instanceof SpatialGridInfoVo) {
			SpatialGridInfoVo info = (SpatialGridInfoVo) entity;
			info.setGridGeom(wktStr);
			info.setGeomJson(geoJsonStr);
			info.setGridArea(BigDecimal.valueOf(area));
		}
	}


	/**
	 * 批量保存网格数据
	 *
	 * @param gridList 网格数据列表
	 * @return 保存结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R batchSaveGrids(List<SpatialGrid> gridList) {
		try {
			// 校验每个网格数据
			for (SpatialGrid grid : gridList) {
				// 检查必填字段
				if (grid.getGridCode() == null || grid.getGridCode().trim().isEmpty()) {
					return R.fail("网格编码不能为空");
				}
				if (grid.getGridName() == null || grid.getGridName().trim().isEmpty()) {
					return R.fail("网格名称不能为空");
				}
				if (grid.getUserId() == null) {
					return R.fail("管理员ID不能为空");
				}

				// 校验网格编码和网格名称是否重复
				if (ValidationUtil.checkBase(grid.getUserId(), grid.getGridCode(), grid.getGridName())) {
					return R.fail("网格数据只能关联管理员角色，或网格编码/名称已存在");
				}

				// 计算多边形面积
				if (grid.getGeomType() != null && grid.getGridGeom() != null) {
					// 从WKT格式中提取坐标
					String wkt = grid.getGridGeom();
					String[] coordinates = extractCoordinatesFromWkt(wkt);

					// 计算面积
					double area = GeometryUtil.calculateArea(grid.getGeomType(), coordinates);
					grid.setGridArea(BigDecimal.valueOf(area));
				}
			}

			// 批量保存
			boolean success = this.saveBatch(gridList);
			if (success) {
				return R.success("成功导入 " + gridList.size() + " 条网格数据");
			} else {
				return R.fail("批量导入失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			return R.fail("批量导入异常: " + e.getMessage());
		}
	}

	/**
	 * 从WKT格式中提取坐标数组
	 *
	 * @param wkt WKT格式的几何数据
	 * @return 坐标数组
	 */
	private String[] extractCoordinatesFromWkt(String wkt) {
		try {
			// 提取坐标部分
			int startIndex = wkt.indexOf("((");
			int endIndex = wkt.indexOf("))");

			if (startIndex >= 0 && endIndex >= 0) {
				String coordStr = wkt.substring(startIndex + 2, endIndex);
				// 分割坐标点，处理可能的空格分隔
				String[] rawCoords = coordStr.split("\\s+");

				// 将坐标转换为"x,y"格式
				List<String> formattedCoords = new ArrayList<>();
				for (int i = 0; i < rawCoords.length; i += 2) {
					if (i + 1 < rawCoords.length) {
						formattedCoords.add(rawCoords[i] + "," + rawCoords[i + 1]);
					}
				}

				return formattedCoords.toArray(new String[0]);
			}
		} catch (Exception e) {
			log.error("从WKT提取坐标失败: {}", e.getMessage());
		}

		return new String[0];
	}

	@Override
	public R getGridListWithSchedule(GridScheduleQueryDTO dto) {
		try {
			QueryWrapper<SpatialGrid> queryWrapper = new QueryWrapper<>();

			// 网格名称模糊搜索
			if (dto.getGridName() != null && !dto.getGridName().isEmpty()) {
				queryWrapper.like("grid_name", dto.getGridName());
			}

			// 添加管理员ID筛选条件
			if (dto.getId() != null) {
				queryWrapper.eq("user_id", dto.getId());
			}

			// 添加排序条件
			if ("1".equals(dto.getSortBy())) {
				queryWrapper.orderByDesc("grid_code");
			} else {
				queryWrapper.orderByAsc("grid_code");
			}

			// 获取基础网格列表
			List<SpatialGrid> gridList = this.list(queryWrapper);

			// 提前一次性查询当日所有网格的排班信息
			LocalDate today = LocalDate.now();
			List<Long> gridIds = gridList.stream().map(SpatialGrid::getId).collect(Collectors.toList());

			// 查询今日排班信息
			List<GridSchedule> allTodaySchedules = queryTodaySchedules(gridIds, today);

			// 查询所有管理员信息
			Map<Long, String> inspectorNameMap = queryInspectorNames(gridList);

			// 创建网格ID到排班信息的映射
			Map<Long, String> gridScheduleMap = buildGridScheduleMap(allTodaySchedules);

			// 转换为VO对象
			List<SpatialGridListVo> result = convertToGridListVo(gridList, inspectorNameMap, gridScheduleMap);

			// 添加排班状态筛选条件
			result = filterByScheduleStatus(result, dto.getScheduleStatus());

			return R.data(result);

		} catch (Exception e) {
			log.error("获取网格列表失败", e);
			return R.fail("获取网格列表失败：" + e.getMessage());
		}
	}

	/**
	 * 查询今日排班信息
	 *
	 * @param gridIds 网格ID列表
	 * @param today 当前日期
	 * @return 排班信息列表
	 */
	private List<GridSchedule> queryTodaySchedules(List<Long> gridIds, LocalDate today) {
		LambdaQueryWrapper<GridSchedule> scheduleWrapper = new LambdaQueryWrapper<>();
		scheduleWrapper.eq(GridSchedule::getIsDeleted, 0)
			.eq(GridSchedule::getScheduleDate, today);

		if (!gridIds.isEmpty()) {
			scheduleWrapper.in(GridSchedule::getGridId, gridIds);
		}

		return gridScheduleService.list(scheduleWrapper);
	}

	/**
	 * 查询管理员姓名信息
	 *
	 * @param gridList 网格列表
	 * @return 管理员ID到姓名的映射
	 */
	private Map<Long, String> queryInspectorNames(List<SpatialGrid> gridList) {
		List<Long> userIds = gridList.stream().map(SpatialGrid::getUserId).distinct().collect(Collectors.toList());
		Map<Long, String> inspectorNameMap = new HashMap<>();
		if (!userIds.isEmpty()) {
			List<User> inspectors = userService.listByIds(userIds);
			inspectorNameMap = inspectors.stream()
				.collect(Collectors.toMap(
					User::getId,
					User::getRealName,
					// 如果有重复键，保留第一个值
					(existing, replacement) -> existing
				));
		}
		return inspectorNameMap;
	}

	/**
	 * 构建网格ID到排班信息的映射
	 *
	 * @param allTodaySchedules 今日排班信息列表
	 * @return 网格ID到排班人员姓名的映射
	 */
	private Map<Long, String> buildGridScheduleMap(List<GridSchedule> allTodaySchedules) {
		Map<Long, String> gridScheduleMap = new HashMap<>();
		Map<Long, Long> gridToStaffIdMap = new HashMap<>();

		// 先保存网格ID到员工ID的映射和初始排班信息
		for (GridSchedule schedule : allTodaySchedules) {
			gridToStaffIdMap.put(schedule.getGridId(), schedule.getStaffId());
			gridScheduleMap.put(schedule.getGridId(), schedule.getStaffName());
		}

		// 查询所有排班人员的最新信息
		List<Long> staffIds = allTodaySchedules.stream().map(GridSchedule::getStaffId).distinct().collect(Collectors.toList());
		if (!staffIds.isEmpty()) {
			List<User> staffUsers = userService.listByIds(staffIds);
			Map<Long, String> staffNameMap = staffUsers.stream()
				.collect(Collectors.toMap(
					User::getId,
					User::getRealName,
					// 如果有重复键，保留第一个值
					(existing, replacement) -> existing
				));

			// 使用最新的用户姓名更新排班信息
			for (Map.Entry<Long, Long> entry : gridToStaffIdMap.entrySet()) {
				Long gridId = entry.getKey();
				Long staffId = entry.getValue();
				String latestStaffName = staffNameMap.get(staffId);
				if (latestStaffName != null) {
					// 使用最新的用户姓名替换排班表中的staffName
					gridScheduleMap.put(gridId, latestStaffName);
				}
			}
		}

		return gridScheduleMap;
	}

	/**
	 * 将网格列表转换为VO对象列表
	 *
	 * @param gridList 网格列表
	 * @param inspectorNameMap 管理员姓名映射
	 * @param gridScheduleMap 排班信息映射
	 * @return VO对象列表
	 */
	private List<SpatialGridListVo> convertToGridListVo(List<SpatialGrid> gridList, Map<Long, String> inspectorNameMap, Map<Long, String> gridScheduleMap) {
		return gridList.stream().map(grid -> {
			SpatialGridListVo vo = new SpatialGridListVo();
			vo.setId(grid.getId());
			vo.setGridCode(grid.getGridCode());
			vo.setGridName(grid.getGridName());
			// 从网格排班映射中获取排班信息，不存在则返回空字符串
			vo.setTodaySchedule(gridScheduleMap.getOrDefault(grid.getId(),""));
			// 从Map中获取管理员姓名，不存在则返回"-"
			vo.setInspectorName(inspectorNameMap.getOrDefault(grid.getUserId(), "-"));
			return vo;
		}).collect(Collectors.toList());
	}

	/**
	 * 根据排班状态筛选网格列表
	 *
	 * @param gridListVos 网格VO列表
	 * @param scheduleStatus 排班状态
	 * @return 筛选后的网格VO列表
	 */
	private List<SpatialGridListVo> filterByScheduleStatus(List<SpatialGridListVo> gridListVos, String scheduleStatus) {
		if (scheduleStatus == null || scheduleStatus.isEmpty()) {
			return gridListVos;
		}

		return gridListVos.stream().filter(vo -> {
			// 当日已排班
			if ("1".equals(scheduleStatus)) {
				return vo.getTodaySchedule() != null && !vo.getTodaySchedule().isEmpty();
			}
			// 当日未排班
			else if ("2".equals(scheduleStatus)) {
				return vo.getTodaySchedule() == null || vo.getTodaySchedule().isEmpty();
			}
			return true;
		}).collect(Collectors.toList());
	}


}
