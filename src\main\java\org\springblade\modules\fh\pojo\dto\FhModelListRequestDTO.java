package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型列表请求DTO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "模型列表请求参数")
public class FhModelListRequestDTO {

    @Schema(description = "开始时间戳 (毫秒)")
    private Long startTime;

    @Schema(description = "结束时间戳 (毫秒)")
    private Long endTime;

    @Schema(description = "文件类型")
    private String fileType;
}