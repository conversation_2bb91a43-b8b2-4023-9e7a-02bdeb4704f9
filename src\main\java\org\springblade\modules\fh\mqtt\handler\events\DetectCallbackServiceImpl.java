package org.springblade.modules.fh.mqtt.handler.events;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.DetectResultDTO;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.service.IBeachLitterMediaService;
import org.springblade.modules.beachwaste.service.IEventService;
import org.springblade.modules.fh.mqtt.handler.util.GeometryUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 回调检测结果处理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DetectCallbackServiceImpl implements IDetectCallbackService {

    private final IBeachLitterMediaService beachLitterMediaService;
    private final IEventService eventService;

    @Override
    public R<String> handleDetectBatchCallback(Object requestBody) {
        log.info("收到垃圾识别批量检测回调，请求体类型: {}", requestBody != null ? requestBody.getClass().getName() : "null");

        // 记录请求体内容，便于调试
        if (requestBody != null) {
            try {
                log.info("请求体内容: {}", JSON.toJSONString(requestBody));
            } catch (Exception e) {
                log.warn("无法序列化请求体内容: {}", e.getMessage());
            }
        }

        List<DetectResultDTO> records;

        try {
			// 解析请求体并返回检查结果集合
            records = parseRequestBody(requestBody);
        } catch (Exception e) {
            log.error("解析请求体时发生异常", e);
            return R.fail("解析请求体失败: " + e.getMessage());
        }

		// 事件对象生成并存储
        return processDetectResults(records);
    }

    /**
     * 解析请求体数据
     *
     * @param requestBody 请求体对象
     * @return 检测结果DTO列表
     */
    private List<DetectResultDTO> parseRequestBody(Object requestBody) {
        if (requestBody == null) {
            return new ArrayList<>();
        }

        List<Object> rawList;

        // 处理不同格式的请求体
        if (requestBody instanceof List) {
            // 如果是数组格式，直接使用
            rawList = (List<Object>) requestBody;
            log.info("检测到数组格式的请求体，已转换为DetectResultDTO对象列表");
        } else if (requestBody instanceof Map) {
            // 如果是对象格式，尝试获取records字段
            Map<String, Object> requestMap = (Map<String, Object>) requestBody;
            if (requestMap.containsKey("records") && requestMap.get("records") instanceof List) {
                // 获取records字段的值
                rawList = (List<Object>) requestMap.get("records");
                log.info("检测到对象格式的请求体，包含records字段，已转换为DetectResultDTO对象列表");
            } else {
                log.warn("请求体格式不正确，缺少records字段或格式错误: {}", JSON.toJSONString(requestMap));
                throw new IllegalArgumentException("请求体格式不正确，缺少records字段或格式错误");
            }
        } else {
            log.warn("不支持的请求体格式: {}", requestBody.getClass().getName());
            throw new IllegalArgumentException("不支持的请求体格式，仅支持数组或包含records字段的对象");
        }

		// 将原始对象列表解析为DetectResultDTO对象列表
        return parseDetectResultDTOs(rawList);
    }

    /**
     * 将原始对象列表解析为DetectResultDTO对象列表
     *
     * @param rawList 原始对象列表
     * @return DetectResultDTO对象列表
     */
    private List<DetectResultDTO> parseDetectResultDTOs(List<Object> rawList) {
        List<DetectResultDTO> dtoList = new ArrayList<>(rawList.size());
        for (Object item : rawList) {
            try {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(item));
                Object infoObject = jsonObject.get("info");

                if (infoObject instanceof String) {
                    // 如果 info 是字符串，尝试将其解析为 List<Object>
                    try {
                        List<Object> parsedInfoList = JSON.parseArray((String) infoObject, Object.class);
                        jsonObject.put("info", parsedInfoList);
                    } catch (Exception e) {
                        log.warn("无法将 info 字段的字符串内容解析为JSON数组: {}", infoObject, e);
                    }
                }
                // 将处理后的 JSONObject 转换为 DetectResultDTO
                DetectResultDTO dto = jsonObject.toJavaObject(DetectResultDTO.class);
                dtoList.add(dto);
            } catch (Exception e) {
                log.error("解析DetectResultDTO时发生异常, item: {}", JSON.toJSONString(item), e);
            }
        }
        return dtoList;
    }

    @Override
    public R<String> processDetectResults(List<DetectResultDTO> records) {
        try {
            // 参数校验
            if (records == null || records.isEmpty()) {
                log.warn("回调数据为空");
                return R.fail("回调数据为空");
            }

            log.info("处理批量识别结果，数据条数: {}", records.size());

            // 处理识别结果并生成事件列表
            List<Event> events = new ArrayList<>();

            // 遍历处理每条识别结果
            for (DetectResultDTO resultDTO : records) {
                // 记录处理开始
                log.info("处理识别结果: {}", JSON.toJSONString(resultDTO));

                // 跳过无效记录
                if (resultDTO == null) {
                    log.warn("识别结果对象为空");
                    continue;
                }

                if (resultDTO.getFileMd5() == null) {
                    log.warn("数据缺少必填参数fileMd5: {}", JSON.toJSONString(resultDTO));
                    continue;
                }

                // 查找媒体文件记录
                BeachLitterMedia media = getMediaByMd5(resultDTO.getFileMd5());
                if (media == null) {
                    log.warn("未找到对应的媒体文件记录, fileMd5: {}", resultDTO.getFileMd5());
                    continue;
                }

                // 处理空的识别结果
                if (resultDTO.getInfo() == null || resultDTO.getInfo().isEmpty()) {
                    // 更新媒体文件的空结果状态
                    media.setAiStatus(true);
                    media.setAiResult("0");
                    beachLitterMediaService.updateById(media);
                    log.info("AI检测结果info数组为空，跳过事件生成, fileMd5: {}", media.getFileMd5());
                    continue;
                }

                // 记录关键字段值，便于调试
                log.info("处理识别结果，fileMd5: {}, confidence: {}, info数组大小: {}",
                        resultDTO.getFileMd5(),
                        resultDTO.getConfidence(),
                        resultDTO.getInfo() != null ? resultDTO.getInfo().size() : 0);

                // 更新媒体文件的AI识别结果
                media.setAiStatus(true);
                media.setAiResult(String.valueOf(resultDTO.getInfo().size()));
                beachLitterMediaService.updateById(media);

                // 为每个识别项创建事件
                for (Object item : resultDTO.getInfo()) {
                    // 将item转换为DetectResultDTO对象
                    DetectResultDTO itemDTO = JSON.parseObject(JSON.toJSONString(item), DetectResultDTO.class);

                    // 创建事件对象并设置基本属性
                    Event event = new Event();

                    // 设置基本信息
                    event.setDiscoveryTime(DateTime.now());
                    event.setDiscoveryImagePath(media.getObjectKey());
                    event.setDiscoveryMethod(0L);
                    event.setEventStatus(0L);

                    // 设置位置信息
                    if (resultDTO.getLat() != null && resultDTO.getLon() != null) {
						getGridIdAndLocation(resultDTO, event);
					} else if(itemDTO.getLat() != null && itemDTO.getLon() != null) {
						getGridIdAndLocation(itemDTO, event);
					} else {
						event.setGridId(0L);
						event.setLocation(media.getLocation() != null ? media.getLocation() : "0.0,0.0");
					}

                    // 设置垃圾分类信息 设置置信度并转换为百分比格式
                    if (itemDTO.getConfidence() != null) {
                        int percentage = (int) Math.round(itemDTO.getConfidence() * 100);
                        event.setConfidence(BigDecimal.valueOf(percentage));
                    }

                    // 设置垃圾材质
                    if (itemDTO.getClassId() != null) {
                        event.setWasteMaterial(Long.valueOf(itemDTO.getClassId()));
                    } else {
                        event.setWasteMaterial(99L);
                    }

                    // 设置垃圾大小
                    if (itemDTO.getSizeClassId() != null) {
                        event.setWasteSize(Long.valueOf(itemDTO.getSizeClassId()));
                    } else {
						// 如果为空时设置为最小尺度的垃圾
                        event.setWasteSize(4L);
                    }

                    // 设置边界框
                    event.setBox(String.valueOf(itemDTO.getBox()));

                    // 添加到事件列表
                    events.add(event);
                }
            }

            // 批量保存事件
            if (events.isEmpty()) {
                return R.success("没有有效数据需要处理");
            }

            boolean saved = eventService.saveBatch(events);
            if (saved) {
                return R.success("成功处理 " + events.size() + " 条识别结果");
            } else {
                return R.fail("数据保存失败");
            }

        } catch (Exception e) {
            log.error("处理检测批量回调异常", e);
            return R.fail("系统异常: " + e.getMessage());
        }
    }

    /**
     * 根据检测结果DTO中的经纬度信息，计算并设置事件对象的网格ID和地理位置
     *
     * @param itemDTO 包含经度和纬度信息的检测结果数据传输对象
     * @param event   需要设置网格ID和地理位置的事件对象
     *
     * 该方法通过经纬度调用几何工具类，将计算得到的网格ID转换为长整型后设置到事件对象中，
     * 同时将经纬度转换为地理坐标点格式也设置到事件对象中。这两个操作共同完成了事件地理位置信息的标准化处理。
     */
    private static void getGridIdAndLocation(DetectResultDTO itemDTO, Event event) {
        String lon = itemDTO.getLon();
        String lat = itemDTO.getLat();

        // 使用几何工具类计算网格ID并转换为长整型，设置到事件对象中
        event.setGridId(Long.valueOf(GeometryUtil.getGridId(lon, lat)));

        // 将经纬度转换为地理坐标点格式，设置到事件对象中
        event.setLocation(GeometryUtil.getPoint(lon, lat));
    }

	/**
     * 根据MD5查询媒体文件
     *
     * @param fileMd5 文件MD5值
     * @return 媒体文件实体，如果不存在则返回null
     */
    private BeachLitterMedia getMediaByMd5(String fileMd5) {
        if (fileMd5 == null) {
            return null;
        }

        return beachLitterMediaService.lambdaQuery()
                .eq(BeachLitterMedia::getFileMd5, fileMd5)
                .last("LIMIT 1")
                .one();
    }

}
