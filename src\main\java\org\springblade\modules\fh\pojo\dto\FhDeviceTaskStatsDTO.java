package org.springblade.modules.fh.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 设备任务统计请求DTO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "设备任务统计请求参数")
public class FhDeviceTaskStatsDTO {

    @NotNull(message = "年份不能为空")
    @Min(value = 1900, message = "年份不合法")
    @Schema(description = "年份", required = true, example = "2023")
    private Integer year;
}
