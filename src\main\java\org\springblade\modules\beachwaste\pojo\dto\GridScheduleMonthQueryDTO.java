package org.springblade.modules.beachwaste.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 网格排班按月查询 DTO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "网格排班按月查询 DTO")
public class GridScheduleMonthQueryDTO {

    @NotNull(message = "网格ID不能为空")
    @Schema(description = "网格ID", required = true)
    private Long gridId;

    @NotNull(message = "年份不能为空")
    @Min(value = 1900, message = "年份不合法")
    @Schema(description = "年份", required = true, example = "2025")
    private Integer year;

    @Min(value = 1, message = "月份不合法")
    @Max(value = 12, message = "月份不合法")
    @Schema(description = "月份", required = true, example = "7")
    private Integer month;

}
