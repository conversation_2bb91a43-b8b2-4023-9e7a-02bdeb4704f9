package org.springblade.modules.fh.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 设备任务统计结果VO
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "设备任务统计结果")
public class FhDeviceTaskStatsVO {

	@Schema(description = "月份")
	private Integer month;

    @Schema(description = "月度任务统计")
    private List<MonthlyStats> monthlyStats;

    /**
     * 月度统计数据
     */
    @Data
    @Schema(description = "月度统计数据")
    public static class MonthlyStats {

		@Schema(description = "设备SN")
		private String deviceSn;

		@Schema(description = "设备名称")
		private String deviceName;

        @Schema(description = "任务数量")
        private Long count;
    }
}
