package org.springblade.modules.fh.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.pojo.entity.FhOrgSetting;
import org.springblade.modules.fh.service.IFhConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping( "/fh/config")
@Tag(name = "司空配置相关", description = "司空配置相关")
public class FhConfigController {
	@Resource
	private IFhConfigService fhConfigService;

	@PostMapping("/test")
	public void topicSubscribe(@RequestParam String topic){
	}

	@GetMapping("/org-setting/get")
	@Operation(summary = "获取司空配置")
	public R<List<FhOrgSetting>> getOrgSetting(){
		List<FhOrgSetting> list = fhConfigService.getFhOrgSetting();
		return R.data(list);
	}

	@PostMapping("/org-setting/set")
	@Operation(summary = "设置司空配置")
	public R<Boolean> orgSetting(@RequestBody List<FhOrgSetting> list){
		Boolean b = fhConfigService.fhOrgSetting(list);

		return R.status(b);
	}

}
