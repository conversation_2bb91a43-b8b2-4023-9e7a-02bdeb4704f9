package org.springblade.modules.beachwaste.util;

import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.GridScheduleRangeDTO;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 网格排班校验工具类
 * 负责处理网格排班相关的校验逻辑
 *
 * <AUTHOR> AI
 */
@Component
public class GridScheduleValidationUtil {

    private static ISpatialGridService spatialGridService;
    private static IUserService userService;

    @Autowired
    public void setSpatialGridService(ISpatialGridService spatialGridService) {
        GridScheduleValidationUtil.spatialGridService = spatialGridService;
    }

    @Autowired
    public void setUserService(IUserService userService) {
        GridScheduleValidationUtil.userService = userService;
    }

    /**
     * 校验排班参数基础信息
     *
     * @param rangeDTO 排班信息DTO
     * @return 校验结果，null表示校验通过，否则返回错误信息
     */
    public static R validateScheduleParams(GridScheduleRangeDTO rangeDTO) {
        if (rangeDTO.getGridId() == null || spatialGridService.getById(rangeDTO.getGridId()) == null) {
            return R.fail("网格ID不存在");
        }
        if (rangeDTO.getStaffId() == null || userService.getById(rangeDTO.getStaffId()) == null) {
            return R.fail("人员ID不存在");
        }

        // 校验单个时间范围的情况
        if (rangeDTO.getTimeRanges() == null || rangeDTO.getTimeRanges().isEmpty()) {
            if (rangeDTO.getStartDate() == null || rangeDTO.getEndDate() == null) {
                return R.fail("必须提供时间范围或时间范围列表");
            }
            return null;
        }

        // 校验多个时间范围的情况
        for (GridScheduleRangeDTO.TimeRange timeRange : rangeDTO.getTimeRanges()) {
            if (timeRange.getStartDate() == null || timeRange.getEndDate() == null) {
                return R.fail("时间范围中的开始日期和结束日期不能为空");
            }
            if (timeRange.getStartDate().isAfter(timeRange.getEndDate())) {
                return R.fail("时间范围中的开始日期不能晚于结束日期");
            }
        }

        return null;
    }
}
