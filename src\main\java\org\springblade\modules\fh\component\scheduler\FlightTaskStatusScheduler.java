package org.springblade.modules.fh.component.scheduler;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.modules.fh.mapper.FhFlightTaskMapper;
import org.springblade.modules.fh.pojo.dto.FhFlightTaskRequestByTimeBaseDTO;
import org.springblade.modules.fh.pojo.entity.FTExceptions;
import org.springblade.modules.fh.pojo.entity.FTOperations;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;
import org.springblade.modules.fh.pojo.vo.FhFlightTaskVO;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 飞行任务状态定时检查更新
 * 定时查询飞行任务表中的数据，获取当前时间之前更新时间为空的数据
 * 通过司空API查询是否飞行完成并更新对应任务数据
 *
 * <AUTHOR> AI
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class FlightTaskStatusScheduler {

    private final FhFlightTaskMapper fhFlightTaskMapper;
    private final FhOpenApiHttpUtil fhOpenApiHttpUtil;

    /**
     * 每一小时执行一次，检查未更新的飞行任务状态
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkAndUpdateFlightTaskStatus() {
        log.info("开始执行飞行任务状态检查定时任务");

        try {
            // 查询当前时间之前更新时间为空的飞行任务
            List<FhFlightTask> uncompletedTasks = findUncompletedFlightTasks();

            if (uncompletedTasks.isEmpty()) {
                log.info("没有需要更新的飞行任务");
                return;
            }

            log.info("找到{}个需要检查状态的飞行任务", uncompletedTasks.size());
			checkAndUpdateTaskStatus(uncompletedTasks);
            log.info("飞行任务状态检查定时任务执行完成");
        } catch (Exception e) {
            log.error("执行飞行任务状态检查定时任务出错", e);
        }
    }

    /**
     * 查询未完成的飞行任务
     * 条件：当前时间之前的任务 && 更新时间为空
     */
    private List<FhFlightTask> findUncompletedFlightTasks() {
        return fhFlightTaskMapper.selectList(
            new LambdaQueryWrapper<FhFlightTask>()
				// 完成时间为空
                .isNull(FhFlightTask::getCompletedAt)
				.notIn(FhFlightTask::getTaskStatus, "terminated", "success")
        );
    }

    /**
     * 检查任务状态并更新
     * 通过司空API查询飞行任务状态
     * 将任务按设备SN分组，批量查询任务状态
     */
    private void checkAndUpdateTaskStatus(List<FhFlightTask> uncompletedTasks) {
        if (uncompletedTasks == null || uncompletedTasks.isEmpty()) {
            return;
        }

        // 按设备SN分组任务
        Map<String, List<FhFlightTask>> tasksByDeviceSn = uncompletedTasks.stream()
                .collect(Collectors.groupingBy(FhFlightTask::getSn));

        tasksByDeviceSn.forEach((deviceSn, tasks) -> {
            log.info("开始检查设备[{}]的飞行任务状态，任务数量: {}", deviceSn, tasks.size());

			// 确定查询时间范围
			OffsetDateTime minBeginAt = tasks.stream()
				.map(FhFlightTask::getBeginAt).min(OffsetDateTime::compareTo).orElse(OffsetDateTime.now());

			OffsetDateTime maxBeginAt = tasks.stream()
					.map(FhFlightTask::getBeginAt).max(OffsetDateTime::compareTo).orElse(OffsetDateTime.now());

			// 构建任务列表查询请求DTO
			FhFlightTaskRequestByTimeBaseDTO requestDTO = new FhFlightTaskRequestByTimeBaseDTO();
			// 设置查询时间范围，使用任务的最早开始时间
			requestDTO.setBeginAt(minBeginAt.minusHours(24).toEpochSecond());
			// 结束时间使用最晚开始时间+24小时，确保覆盖所有任务
			requestDTO.setEndAt(maxBeginAt.plusHours(24).toEpochSecond());

			// 设置设备SN
			requestDTO.setDeviceSn(deviceSn);

			// 直接调用OpenAPI获取飞行任务列表
			List<FhFlightTaskVO> completedTasksFromApi = getFlightTaskListFromOpenApi(requestDTO);

			if (completedTasksFromApi != null && !completedTasksFromApi.isEmpty()) {
                // 创建两个集合，用于存储异常数据
                List<FhFlightTask> dbTasksNotInApi = new ArrayList<>();
                List<FhFlightTaskVO> apiTasksNotInDb;

				// 遍历当前设备下的所有未完成任务，检查是否在API返回的已完成列表中
				tasks.forEach(task -> {
					Optional<FhFlightTaskVO> matchedTask = completedTasksFromApi.stream()
							.filter(apiTask -> apiTask.getUuid().equals(task.getUuid()))
							.findFirst();

					if (matchedTask.isPresent()) {
						String status = matchedTask.get().getTaskStatus();
						log.info("飞行任务[{}]当前状态: {}", task.getUuid(), status);

						// 更新任务状态和相关字段，并直接更新数据库
						boolean updateSuccess = updateTaskFromApiResponse(task, matchedTask.get());

						if (updateSuccess) {
							log.info("成功更新飞行任务[{}]状态为[{}]", task.getUuid(), task.getTaskStatus());
						} else {
							log.warn("更新飞行任务[{}]失败", task.getUuid());
						}
					} else {
                        // 数据库中存在但API中不存在的任务，视为异常数据
                        dbTasksNotInApi.add(task);
                    }
				});

                // 查找API返回的任务列表中，在数据库未完成任务列表中找不到的任务
                List<String> dbTaskUuids = tasks.stream()
                    .map(FhFlightTask::getUuid)
                    .collect(Collectors.toList());

                apiTasksNotInDb = completedTasksFromApi.stream()
                    .filter(apiTask -> !dbTaskUuids.contains(apiTask.getUuid()))
                    .collect(Collectors.toList());

                // 处理异常数据：数据库中存在但API中不存在的任务
                if (!dbTasksNotInApi.isEmpty()) {
                    log.info("发现{}个数据库中存在但API中不存在的异常任务", dbTasksNotInApi.size());
                    handleAbnormalTasks(dbTasksNotInApi);
                }

                // 处理异常数据：API中存在但数据库中不存在的任务
                if (!apiTasksNotInDb.isEmpty()) {
                    log.info("发现{}个API中存在但数据库中不存在的异常任务", apiTasksNotInDb.size());
                    // 这些任务可能需要创建并标记为异常，但由于没有完整的数据库实体，暂不处理
                    // 记录这些任务的UUID，方便排查
                    apiTasksNotInDb.forEach(apiTask ->
                        log.info("API中存在但数据库中不存在的异常任务UUID: {}", apiTask.getUuid()));
                }
			}
        });
        log.info("设备分组飞行任务状态检查完成");
    }

    /**
     * 直接调用OpenAPI获取飞行任务列表
     *
     * @param requestDTO 请求参数
     * @return 飞行任务列表
     */
    private List<FhFlightTaskVO> getFlightTaskListFromOpenApi(FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        try {
            log.info("直接调用OpenAPI获取设备[{}]的飞行任务列表，时间范围: {} - {}",
                    requestDTO.getDeviceSn(), requestDTO.getBeginAt(), requestDTO.getEndAt());

			// 使用链式调用构建请求URL
			StringBuilder urlBuilder = new StringBuilder(FhOpenapiPathConstant.FLIGHT_TASK_LIST)
				.append("?sn=").append(requestDTO.getDeviceSn())
				.append("&begin_at=").append(requestDTO.getBeginAt())
				.append("&end_at=").append(requestDTO.getEndAt());


            // 调用OpenAPI
            String responseStr = fhOpenApiHttpUtil.get(urlBuilder.toString());

            // 解析响应结果
            JSONObject responseJson = JSONUtil.parseObj(responseStr);

            // 检查响应状态
            if (responseJson.getInt("code") != 0) {
                log.error("调用OpenAPI获取飞行任务列表失败: {}", responseJson.getStr("message"));
                return new ArrayList<>();
            }

            // 获取数据部分
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                log.warn("OpenAPI返回的数据为空");
                return new ArrayList<>();
            }

            // 获取任务列表
            JSONArray list = data.getJSONArray("list");
            if (list == null || list.isEmpty()) {
                log.warn("OpenAPI返回的任务列表为空");
                return new ArrayList<>();
            }

            // 转换为VO对象列表
            List<FhFlightTaskVO> taskList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                JSONObject taskJson = list.getJSONObject(i);

                // 创建VO对象
                FhFlightTaskVO task = new FhFlightTaskVO();
                task.setUuid(taskJson.getStr("uuid"));
                task.setName(taskJson.getStr("name"));
                task.setTaskType(taskJson.getStr("task_type"));
                task.setTaskStatus(taskJson.getStr("status"));
                task.setSn(taskJson.getStr("sn"));
                task.setWaylineUuid(taskJson.getStr("wayline_uuid"));
                task.setLandingDockSn(taskJson.getStr("landing_dock_sn"));
                task.setBeginAt(taskJson.getStr("begin_at"));
                task.setEndAt(taskJson.getStr("end_at"));
                task.setRunAt(taskJson.getStr("run_at"));
                task.setCompletedAt(taskJson.getStr("completed_at"));

				// 处理嵌套对象：操作记录
				if (taskJson.containsKey("operations") && !taskJson.isNull("operations")) {
					JSONObject operationsJson = taskJson.getJSONObject("operations");
					FTOperations operations = new FTOperations();
					operations.setOperatorAccount(operationsJson.getStr("operator_account"));
					task.setOperations(operations);
				}

                // 处理嵌套对象：异常信息
				if (taskJson.containsKey("exceptions")) {
					JSONArray exceptionsArray = taskJson.getJSONArray("exceptions");
					if (exceptionsArray != null && !exceptionsArray.isEmpty()) {
						// Check if there are any exceptions
						JSONObject exceptionsJson = exceptionsArray.getJSONObject(0);
						// Take the first exception
						FTExceptions exceptions = new FTExceptions();
						exceptions.setHappenAt(exceptionsJson.getStr("happen_at"));
						exceptions.setMessage(exceptionsJson.getStr("message"));
						task.setExceptions(exceptions);
					}
				}

                taskList.add(task);
            }

            log.info("成功从OpenAPI获取到{}个飞行任务", taskList.size());
            return taskList;

        } catch (Exception e) {
            log.error("调用OpenAPI获取飞行任务列表时发生异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理异常任务数据
     * 将数据库中存在但API中不存在的任务标记为异常数据（isDeleted=3）
     *
     * @param abnormalTasks 异常任务列表
     */
    private void handleAbnormalTasks(List<FhFlightTask> abnormalTasks) {
        if (abnormalTasks == null || abnormalTasks.isEmpty()) {
            return;
        }

        log.info("开始处理{}个异常任务数据", abnormalTasks.size());

        try {
            for (FhFlightTask task : abnormalTasks) {
                // 将isDeleted字段设置为3，表示异常数据
                task.setIsDeleted(3);

                // 更新数据库
                int result = fhFlightTaskMapper.updateById(task);
                if (result > 0) {
                    log.info("成功将异常飞行任务[{}]标记为已删除状态(isDeleted=3)", task.getUuid());
                } else {
                    log.warn("标记异常飞行任务[{}]失败", task.getUuid());
                }
            }

            log.info("异常任务数据处理完成，共处理{}个任务", abnormalTasks.size());
        } catch (Exception e) {
            log.error("处理异常任务数据时发生错误", e);
        }
    }

    /**
     * 根据API响应更新任务对象并更新数据库
     * 将API返回的飞行任务信息中的字段值更新到本地任务对象中，并直接更新数据库
     *
     * @param task 本地任务对象
     * @param apiTask API返回的任务对象
     * @return 是否更新成功
     */
    private boolean updateTaskFromApiResponse(FhFlightTask task, FhFlightTaskVO apiTask) {
        try {
            // 更新基本字段
            task.setTaskStatus(apiTask.getTaskStatus());
            task.setName(apiTask.getName());
            task.setTaskType(apiTask.getTaskType());
            task.setWaylineUuid(apiTask.getWaylineUuid());
            task.setLandingDockSn(apiTask.getLandingDockSn());
            task.setCurrentWaypointIndex(apiTask.getCurrentWaypointIndex());
            task.setTotalWaypoints(apiTask.getTotalWaypoints());
            task.setFolderId(apiTask.getFolderId());
            task.setMediaUploadStatus(apiTask.getMediaUploadStatus());
            task.setResumableStatus(apiTask.getResumableStatus());
            task.setIsBreakPointResume(apiTask.getIsBreakPointResume());

            // 更新时间字段，需要进行格式转换
            if (apiTask.getRunAt() != null && !apiTask.getRunAt().isEmpty()) {
                try {
                    task.setRunAt(OffsetDateTime.parse(apiTask.getRunAt()));
                } catch (Exception e) {
                    log.warn("解析任务执行时间失败: {}", e.getMessage());
                }
            }

            if (apiTask.getCompletedAt() != null && !apiTask.getCompletedAt().isEmpty()) {
                try {
                    task.setCompletedAt(OffsetDateTime.parse(apiTask.getCompletedAt()));
                } catch (Exception e) {
                    log.warn("解析任务完成时间失败: {}", e.getMessage());
                }
            }

            // 更新操作记录
            if (apiTask.getOperations() != null) {
                task.setOperatorAccount(apiTask.getOperations().getOperatorAccount());
            }

            // 更新异常信息
            if (apiTask.getExceptions() != null) {
                try {
                    // 安全地将Long转换为Integer，避免数值溢出异常
                    task.setExceptionCode(apiTask.getExceptions().getCode() <= Integer.MAX_VALUE ?
                        (int)apiTask.getExceptions().getCode() : 0);
                    task.setExceptionMessage(apiTask.getExceptions().getMessage());
                    task.setExceptionSn(apiTask.getExceptions().getSn());

                    if (apiTask.getExceptions().getHappenAt() != null && !apiTask.getExceptions().getHappenAt().isEmpty()) {
                        try {
                            task.setExceptionHappenAt(OffsetDateTime.parse(apiTask.getExceptions().getHappenAt()));
                        } catch (Exception e) {
                            log.warn("解析异常发生时间失败: {}", e.getMessage());
                        }
                    }
                } catch (Exception ex) {
                    log.warn("设置异常信息失败: {}", ex.getMessage());
                }
            }

            // 直接更新数据库
            int result = fhFlightTaskMapper.updateById(task);
            if (result > 0) {
                log.info("成功更新飞行任务[{}]的所有数据项到数据库", task.getUuid());
                return true;
            } else {
                log.warn("更新飞行任务[{}]到数据库失败，可能任务不存在", task.getUuid());
                return false;
            }
        } catch (Exception e) {
            log.error("更新飞行任务[{}]数据时发生异常: {}", task.getUuid(), e.getMessage(), e);
            return false;
        }
    }

}
