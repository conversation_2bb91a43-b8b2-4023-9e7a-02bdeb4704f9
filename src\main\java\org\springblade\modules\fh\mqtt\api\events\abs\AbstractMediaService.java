package org.springblade.modules.fh.mqtt.api.events.abs;

import org.springblade.common.constant.ChannelName;
import org.springblade.modules.fh.mqtt.MqttReply;
import org.springblade.modules.fh.mqtt.api.events.FileUploadCallback;
import org.springblade.modules.fh.mqtt.handler.events.TopicEventsRequest;
import org.springblade.modules.fh.mqtt.handler.events.TopicEventsResponse;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.MessageHeaders;



public abstract class AbstractMediaService {

    @ServiceActivator(inputChannel = ChannelName.INBOUND_EVENTS_FILE_UPLOAD_CALLBACK, outputChannel = ChannelName.OUTBOUND_EVENTS)
    public TopicEventsResponse<MqttReply> fileUploadCallback(TopicEventsRequest<FileUploadCallback> request, MessageHeaders headers) {
        throw new UnsupportedOperationException("fileUploadCallback not implemented");
    }

}
