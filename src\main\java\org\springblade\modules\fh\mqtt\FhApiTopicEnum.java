package org.springblade.modules.fh.mqtt;

import lombok.Getter;
import org.springblade.common.constant.ChannelName;

import java.util.Arrays;
import java.util.regex.Pattern;

import static org.springblade.modules.fh.mqtt.TopicConst.*;


@Getter
public enum FhApiTopicEnum {


    OSD(Pattern.compile("^" + THING_MODEL_PRE + PRODUCT + REGEX_SN + OSD_SUF + "$"), ChannelName.INBOUND_OSD),

    EVENTS(Pattern.compile("^" + THING_MODEL_PRE + PRODUCT + REGEX_SN + EVENTS_SUF + "$"), ChannelName.INBOUND_EVENTS),

    UNKNOWN(Pattern.compile("^.*$"), ChannelName.DEFAULT);

    private final Pattern pattern;

    private final String beanName;

    FhApiTopicEnum(Pattern pattern, String beanName) {
        this.pattern = pattern;
        this.beanName = beanName;
    }

	public static FhApiTopicEnum find(String topic) {
        return Arrays.stream(FhApiTopicEnum.values()).filter(topicEnum -> topicEnum.pattern.matcher(topic).matches()).findAny().orElse(UNKNOWN);
    }
}
