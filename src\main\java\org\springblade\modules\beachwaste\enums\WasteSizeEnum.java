package org.springblade.modules.beachwaste.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 垃圾尺寸分类枚举
 * 特大块垃圾、大块垃圾、中块垃圾、小块垃圾
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum WasteSizeEnum {

	/**
	 * 极小碎屑垃圾或忽略
	 */
	TINY(4L, "极小碎屑垃圾或忽略", 0.0, 2.5),

    /**
     * 2.5cm≤d＜10 cm
     */
    MICRO(3L, "小块垃圾", 2.5, 10.0),

    /**
     * 10cm≤d＜30cm
     */
    SMALL(2L, "中块垃圾", 10.0, 30.0),

    /**
     * 30cm≤d＜1 m
     */
    MEDIUM(1L, "大块垃圾", 30.0, 100.0),

    /**
     * ≥1 m
     */
    LARGE(0L, "特大块垃圾", 100.0, Double.MAX_VALUE);

    /**
     * 尺寸ID
     */
    private final Long id;

    /**
     * 尺寸名称
     */
    private final String name;

    /**
     * 尺寸最小值（厘米）
     */
    private final Double sizeMin;

    /**
     * 尺寸最大值（厘米）
     */
    private final Double sizeMax;

    /**
     * 根据ID获取枚举
     *
     * @param id ID
     * @return 枚举
     */
    public static WasteSizeEnum getById(Long id) {
        if (id == null) {
            return null;
        }
        for (WasteSizeEnum item : values()) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

	/**
     * 根据ID获取枚举名称
     *
     * @param id ID
     * @return 枚举名称
     */
    public static String getDesc(Long id) {
        WasteSizeEnum item = getById(id);
        return item != null ? item.getName() : null;
    }

    /**
     * 判断给定尺寸是否在当前枚举范围内
     *
     * @param size 尺寸（厘米）
     * @return 是否在范围内
     */
    public boolean isInRange(Double size) {
        if (size == null) {
            return false;
        }
        return size >= this.sizeMin && size < this.sizeMax;
    }

    /**
     * 根据尺寸获取对应的枚举值
     *
     * @param size 尺寸（厘米）
     * @return 对应的枚举值
     */
    public static WasteSizeEnum getBySize(Double size) {
        if (size == null) {
            return null;
        }
        for (WasteSizeEnum item : values()) {
            if (item.isInRange(size)) {
                return item;
            }
        }
        return null;
    }


}
