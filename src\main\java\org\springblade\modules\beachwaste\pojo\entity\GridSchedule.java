package org.springblade.modules.beachwaste.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.time.LocalDate;

/**
 * 网格排班信息表
 *
 * <AUTHOR> AI
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("grid_schedule")
public class GridSchedule extends BaseEntity {

    /**
     * 排班ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 网格ID
     */
    private Long gridId;

    /**
     * 人员ID
     */
    private Long staffId;

	/**
	 * 人员姓名
	 */
	private String staffName;

    /**
     * 排班日期
     */
    private LocalDate scheduleDate;

    /**
     * 备注信息
     */
    private String remark;

}
