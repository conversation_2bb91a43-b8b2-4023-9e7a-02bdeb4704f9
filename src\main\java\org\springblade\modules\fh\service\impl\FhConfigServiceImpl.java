package org.springblade.modules.fh.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.ParamCache;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.fh.mqtt.IMqttTopicService;
import org.springblade.modules.fh.mqtt.handler.events.EventsSubscribe;
import org.springblade.modules.fh.pojo.entity.FhOrgSetting;
import org.springblade.modules.fh.pojo.vo.FhOAPrjDeviceVO;
import org.springblade.modules.fh.service.IFhConfigService;
import org.springblade.modules.fh.service.IFhOrgService;
import org.springblade.modules.system.pojo.FhParamEnum;
import org.springblade.modules.system.pojo.entity.Param;
import org.springblade.modules.system.service.IParamService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.springblade.core.cache.constant.CacheConstant.PARAM_CACHE;

@Service
@Slf4j
public class FhConfigServiceImpl implements IFhConfigService {
	public static final String MEDIA_SAVE_KEY = "fh.media.isSave";
	public static final String MEDIA_AI_KEY = "fh.media.isAi";

	@Resource
	private IParamService paramService;
	@Resource
	private EventsSubscribe eventsSubscribe;
	@Resource
	private IFhOrgService orgService;
	@Resource
	private IMqttTopicService mqttTopicService;

	@Override
	public Boolean fhOrgSetting(List<FhOrgSetting> list) {


		boolean b = paramService.saveOrUpdateBatch(list.stream()
			.map(this::toParam)
			.toList()
		);

		if (b){
			CacheUtil.clear(PARAM_CACHE);
			CacheUtil.clear(PARAM_CACHE, Boolean.FALSE);

			subscribeOrgTopic();
		}

		return b;
	}

	@Override
	public List<FhOrgSetting> getFhOrgSetting() {
		return Arrays.stream(FhParamEnum.values())
			.map(this::toFhOrgSetting)
			.toList();
	}

	@Override
	public Boolean checkMediaSave() {
		String str = ParamCache.getValue(MEDIA_SAVE_KEY);

		if (StrUtil.isBlank(str)){
			return false;
		}

		return Boolean.valueOf(str);
	}

	@Override
	public Boolean checkMediaAi() {
		String str = ParamCache.getValue(MEDIA_AI_KEY);

		if (StrUtil.isBlank(str)){
			return false;
		}
		return Boolean.valueOf(str);
	}

	private FhOrgSetting toFhOrgSetting(FhParamEnum paramEnum){
		FhOrgSetting.FhOrgSettingBuilder builder = FhOrgSetting.builder();

		return Optional.ofNullable(ParamCache.getByKey(paramEnum.getKey()))
			.map(p -> builder.id(p.getId())
				.paramKey(p.getParamKey())
				.paramName(p.getParamName())
				.paramValue(p.getParamValue())
				.build())
			.orElse(builder
				.paramKey(paramEnum.getKey())
				.paramName(paramEnum.getLabel())
				.build());
	}

	private Param toParam(FhOrgSetting entity){
		Param param = new Param();

		param.setId(entity.getId());
		param.setParamKey(entity.getParamKey());
		param.setParamName(entity.getParamName());
		param.setParamValue(entity.getParamValue());

		return param;
	}

	@Override
	public void subscribeOrgTopic(){
		String[] topics = mqttTopicService.getSubscribedTopic();
		if (topics.length > 0){
			mqttTopicService.unsubscribe(topics);
		}

		try{
			List<FhOAPrjDeviceVO> list = orgService.prjDevices();
			if (CollUtil.isEmpty(list)){
				return;
			}

			list.stream()
				.map(FhOAPrjDeviceVO::getGateway)
				.filter(item -> StrUtil.isNotBlank(item.getSn()))
				.forEach(gateway -> eventsSubscribe.subscribe(gateway.getSn()));

		}catch (Exception e){
			log.error(e.getMessage());
			throw new ServiceException("司空配置错误");
		}
	}

}
