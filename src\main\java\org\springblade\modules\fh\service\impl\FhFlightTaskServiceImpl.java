package org.springblade.modules.fh.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.common.util.JsonUtils;
import org.springblade.core.tool.api.R;
import org.springblade.modules.fh.mapper.FhFlightTaskMapper;
import org.springblade.modules.fh.pojo.dto.*;
import org.springblade.modules.fh.pojo.entity.FTExceptions;
import org.springblade.modules.fh.pojo.entity.FTOperations;
import org.springblade.modules.fh.pojo.entity.FhFlightTask;
import org.springblade.modules.fh.pojo.vo.*;
import org.springblade.modules.fh.service.IFhFlightTaskService;
import org.springblade.modules.fh.service.IFhOrgService;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.Year;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 飞行任务管理服务实现类
 *
 * <AUTHOR> AI
 */
@Slf4j
@Service
public class FhFlightTaskServiceImpl implements IFhFlightTaskService {

    @Resource
    private IFhOrgService fhOrgService;

    @Resource
    private FhFlightTaskMapper fhFlightTaskMapper;

    @Resource
    private FhOpenApiHttpUtil fhOpenApiHttpUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<FhFlightTaskVO> getFlightTaskList(FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        // 1. 获取所有drone设备的SN号
        List<String> droneSNList = getDroneSNList();
        if (CollUtil.isEmpty(droneSNList)) {
            log.warn("未获取到任何drone设备SN号");
            return Collections.emptyList();
        }

        // 2. 循环调用大疆API获取每个设备的飞行任务列表
        List<FhFlightTaskVO> resultList = new ArrayList<>();
        for (String sn : droneSNList) {
            try {
                // 设置当前设备SN
                requestDTO.setDeviceSn(sn);
                // 调用大疆API获取飞行任务列表
                List<FhFlightTaskVO> deviceTaskList = getDeviceFlightTaskList(requestDTO);
                if (CollUtil.isNotEmpty(deviceTaskList)) {
                    resultList.addAll(deviceTaskList);
                }
            } catch (Exception e) {
                log.error("获取设备[{}]的飞行任务列表失败", sn, e);
            }
        }

        // 3. 将结果保存到数据库
        if (CollUtil.isNotEmpty(resultList)) {
            saveFlightTaskList(resultList, null);
        }

        return resultList;
    }

    /**
     * 保存飞行任务列表到数据库
     *
     * @param taskList 飞行任务列表
     */
    private void saveFlightTaskList(List<FhFlightTaskVO> taskList, String gridIds) {
        if (CollUtil.isEmpty(taskList)) {
            return;
        }

        log.info("开始保存{}条飞行任务数据到数据库", taskList.size());
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        // 使用Stream API处理任务列表
        List<FhFlightTask> entityList = taskList.stream()
            .map(vo -> {
                try {
                    // 创建实体对象
                    FhFlightTask entity = new FhFlightTask();

                    // 使用反射或BeanUtils复制基本属性
                    BeanUtils.copyProperties(vo, entity, "beginAt", "endAt", "runAt", "completedAt");
					entity.setTaskStatus(vo.getTaskStatus());
					entity.setLandingDockSn(vo.getSn());
					entity.setName(vo.getName() + "-" + vo.getBeginAt().substring(0, 10));

                    // 使用函数式方法处理时间字段
                    parseAndSetDateTime(vo.getBeginAt(), formatter, entity::setBeginAt);
                    parseAndSetDateTime(vo.getEndAt(), formatter, entity::setEndAt);
                    parseAndSetDateTime(vo.getRunAt(), formatter, entity::setRunAt);
                    parseAndSetDateTime(vo.getCompletedAt(), formatter, entity::setCompletedAt);

                    // 设置操作记录
                    if (vo.getOperations() != null) {
                        entity.setOperatorAccount(vo.getOperations().getOperatorAccount());
                    }

                    // 设置异常信息
                    if (vo.getExceptions() != null) {
                        try {
                            // 安全地将Long转换为Integer，避免数值溢出异常
                            entity.setExceptionCode(vo.getExceptions().getCode() <= Integer.MAX_VALUE ?
                                (int)vo.getExceptions().getCode() : 0);
                            entity.setExceptionMessage(vo.getExceptions().getMessage());
                            parseAndSetDateTime(vo.getExceptions().getHappenAt(), formatter, entity::setExceptionHappenAt);
                            entity.setExceptionSn(vo.getExceptions().getSn());
                        } catch (Exception ex) {
                            log.warn("设置异常信息失败: {}", ex.getMessage());
                            // 设置默认值，避免整个转换失败
                            entity.setExceptionCode(0);
                        }
                    }

                    return entity;
                } catch (Exception e) {
                    log.error("转换飞行任务数据失败: {}", vo.getUuid(), e);
                    return null;
                }
            })
            .filter(entity -> entity != null)
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(entityList)) {
            try {
                // 1. 提取所有任务的UUID
                List<String> uuidList = entityList.stream()
                    .map(FhFlightTask::getUuid)
                    .collect(Collectors.toList());

                // 2. 一次性查询所有已存在的任务
                List<FhFlightTask> existingTasks = fhFlightTaskMapper.selectList(
                    new LambdaQueryWrapper<FhFlightTask>()
                        .in(FhFlightTask::getUuid, uuidList)
                );

                // 3. 构建UUID到ID的映射关系
                Map<String, Long> uuidToIdMap = existingTasks.stream()
                    .collect(Collectors.toMap(FhFlightTask::getUuid, FhFlightTask::getId));

                // 4. 分离需要更新和需要插入的记录
                List<FhFlightTask> toUpdateList = new ArrayList<>();
                List<FhFlightTask> toInsertList = new ArrayList<>();

                for (FhFlightTask entity : entityList) {
                    if (uuidToIdMap.containsKey(entity.getUuid())) {
                        // 设置ID并加入更新列表
                        entity.setId(uuidToIdMap.get(entity.getUuid()));
                        toUpdateList.add(entity);
                    } else {
                        // 加入插入列表
                        toInsertList.add(entity);
                    }
                }

                // 5. 批量更新已存在的记录
                if (CollUtil.isNotEmpty(toUpdateList)) {
                    for (FhFlightTask entity : toUpdateList) {
                        fhFlightTaskMapper.updateById(entity);
                    }
                    log.info("成功更新{}条飞行任务数据", toUpdateList.size());
                }

                // 6. 批量插入新记录
                if (CollUtil.isNotEmpty(toInsertList)) {
                    for (FhFlightTask entity : toInsertList) {
						entity.setGridIds(gridIds);
                        fhFlightTaskMapper.insert(entity);
                    }
                    log.info("成功插入{}条飞行任务数据", toInsertList.size());
                }

                log.info("成功保存{}条飞行任务数据到数据库", entityList.size());
            } catch (Exception e) {
                log.error("批量保存飞行任务数据失败", e);
            }
        }
    }

    /**
     * 解析日期时间字符串并设置到实体对象中
     *
     * @param dateTimeStr 日期时间字符串
     * @param formatter 日期格式化器
     * @param setter 设置方法引用
     */
    private void parseAndSetDateTime(String dateTimeStr, DateTimeFormatter formatter, java.util.function.Consumer<OffsetDateTime> setter) {
        if (dateTimeStr != null && !dateTimeStr.isEmpty()) {
            setter.accept(OffsetDateTime.parse(dateTimeStr, formatter).plusHours(8));
        }
    }

    /**
     * 获取所有drone设备的SN号
     *
     * @return drone设备SN号列表
     */
    private List<String> getDroneSNList() {
        try {
            // 调用/prj/devices接口获取项目下的所有设备
            List<FhOAPrjDeviceVO> deviceList = fhOrgService.prjDevices();
            if (CollUtil.isEmpty(deviceList)) {
                return Collections.emptyList();
            }

            // 提取所有drone设备的SN号
            List<String> droneSNList = new ArrayList<>();
            for (FhOAPrjDeviceVO device : deviceList) {
				// 20250521变更：获取机场的sn进行查询，不再根据飞机的sn查询
				droneSNList.add(device.getGateway().getSn());
            }
            return droneSNList;
        } catch (Exception e) {
            log.error("获取机场设备SN号列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取指定设备的飞行任务列表
     *
     * @param requestDTO 请求参数
     * @return 飞行任务列表
     */
    private List<FhFlightTaskVO> getDeviceFlightTaskList(FhFlightTaskRequestByTimeBaseDTO requestDTO) {
        try {
            // 使用链式调用构建请求URL
            StringBuilder urlBuilder = new StringBuilder(FhOpenapiPathConstant.FLIGHT_TASK_LIST)
                .append("?sn=").append(requestDTO.getDeviceSn())
                .append("&begin_at=").append(requestDTO.getBeginAt())
                .append("&end_at=").append(requestDTO.getEndAt());

            // 添加可选参数（使用条件判断）
            Optional.ofNullable(requestDTO.getName())
                .ifPresent(name -> urlBuilder.append("&name=").append(name));

            Optional.ofNullable(requestDTO.getTaskType())
                .ifPresent(type -> urlBuilder.append("&task_type=").append(type.getValue()));

            // 处理状态数组参数
            if (requestDTO.getStatus() != null && requestDTO.getStatus().length > 0) {
                // 使用Stream API处理数组
                Arrays.stream(requestDTO.getStatus())
                    .forEach(status -> urlBuilder.append("&status=").append(status));
            }

            // 调用大疆API并解析响应
            String response = FhOpenApiHttpUtil.get(urlBuilder.toString());
            return parseFlightTaskResponse(response, requestDTO.getDeviceSn());
        } catch (Exception e) {
            log.error("获取设备[{}]的飞行任务列表失败", requestDTO.getDeviceSn(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 解析飞行任务列表响应
     *
     * @param response 响应数据
     * @param deviceSn 设备SN号
     * @return 飞行任务列表
     */
    private List<FhFlightTaskVO> parseFlightTaskResponse(String response, String deviceSn) {
        if (response == null || response.isEmpty()) {
            log.warn("设备[{}]的飞行任务列表响应为空", deviceSn);
            return Collections.emptyList();
        }

        try {
            // 使用FhOpenApiHttpUtil工具类解析响应数据
            JSONObject data = FhOpenApiHttpUtil.resData(response);
            if (data == null) {
                log.warn("设备[{}]的飞行任务列表响应data为空", deviceSn);
                return Collections.emptyList();
            }

            JSONArray list = data.getJSONArray("list");
            if (list == null || list.isEmpty()) {
                log.info("设备[{}]的飞行任务列表为空", deviceSn);
                return Collections.emptyList();
            }

            // 直接将JSONArray转换为List<FhFlightTaskVO>
            List<FhFlightTaskVO> taskList = new ArrayList<>(list.size());

            // 使用Stream API处理JSON数组
            for (int i = 0; i < list.size(); i++) {
                JSONObject taskJson = list.getJSONObject(i);
                // 使用JSONUtil直接转换为对象
                FhFlightTaskVO task = JSONUtil.toBean(taskJson, FhFlightTaskVO.class);

                // 设置状态字段（特殊处理）
                task.setTaskStatus(taskJson.getStr("status"));

                // 处理嵌套对象：操作记录
                if (taskJson.containsKey("operations")) {
                    JSONObject operationsJson = taskJson.getJSONObject("operations");
                    task.setOperations(JSONUtil.toBean(operationsJson, FTOperations.class));
                }

                // 处理嵌套对象：异常信息
                if (taskJson.containsKey("exceptions")) {
                    Object exceptionsObj = taskJson.get("exceptions");
                    if (exceptionsObj instanceof JSONObject) {
                        JSONObject exceptionsJson = (JSONObject) exceptionsObj;
                        task.setExceptions(JSONUtil.toBean(exceptionsJson, FTExceptions.class));
                    } else if (exceptionsObj instanceof JSONArray) {
                        // 如果是数组，可能需要取第一个元素或者其他处理逻辑
                        JSONArray exceptionsArray = (JSONArray) exceptionsObj;
                        if (!exceptionsArray.isEmpty()) {
                            Object firstException = exceptionsArray.get(0);
                            if (firstException instanceof JSONObject) {
                                task.setExceptions(JSONUtil.toBean((JSONObject) firstException, FTExceptions.class));
                            }
                        }
                        log.warn("设备[{}]的飞行任务[{}]异常信息为数组类型", deviceSn, task.getUuid());
                    } else {
                        log.warn("设备[{}]的飞行任务[{}]异常信息类型不支持: {}", deviceSn, task.getUuid(),
                               exceptionsObj != null ? exceptionsObj.getClass().getName() : "null");
                    }
                }
                taskList.add(task);
            }

            return taskList;
        } catch (Exception e) {
            log.error("解析设备[{}]的飞行任务列表响应失败", deviceSn, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Integer> getTaskYears() {
        try {

            List<Integer> resultList = fhFlightTaskMapper.selectTaskYears();

            if (CollUtil.isEmpty(resultList)) {
                log.info("未查询到任何飞行任务年份数据");
                return Collections.emptyList();
            }
            return resultList;
        } catch (Exception e) {
            log.error("获取飞行任务年份列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<FhDeviceTaskStatsVO> getDeviceTaskStats(FhDeviceTaskStatsDTO dto) {
        try {
            // 1. 获取所有drone设备的SN号和名称
            List<FhOAPrjDeviceVO> deviceList = fhOrgService.prjDevices();
            if (CollUtil.isEmpty(deviceList)) {
                log.warn("未获取到任何drone设备");
                return Collections.emptyList();
            }

            // 2. 获取所有设备在指定年份的任务数量
            Map<String, Map<Integer, Long>> deviceMonthlyTasksMap = getAllDevicesMonthlyTasks(deviceList, dto.getYear());

            // 3. 使用Stream API按月份组织数据
            return IntStream.rangeClosed(1, 12)
                .mapToObj(month -> {
                    // 创建月度统计对象
                    FhDeviceTaskStatsVO monthlyStats = new FhDeviceTaskStatsVO();
                    monthlyStats.setMonth(month);

                    // 使用Stream API处理设备列表
                    List<FhDeviceTaskStatsVO.MonthlyStats> deviceStatsList = deviceList.stream()
                        .filter(device -> device.getDrone() != null && device.getDrone().getSn() != null)
                        .map(device -> {
                            String deviceSn = device.getDrone().getSn();
                            String deviceName = device.getDrone().getCallsign();

                            // 创建设备月度统计对象
                            FhDeviceTaskStatsVO.MonthlyStats deviceStats = new FhDeviceTaskStatsVO.MonthlyStats();
                            deviceStats.setDeviceSn(deviceSn);
                            deviceStats.setDeviceName(deviceName);

                            // 获取该设备在当前月份的任务数量，如果没有则为0
                            Map<Integer, Long> deviceMonthlyMap = deviceMonthlyTasksMap.getOrDefault(deviceSn, Collections.emptyMap());
                            deviceStats.setCount(deviceMonthlyMap.getOrDefault(month, 0L));

                            return deviceStats;
                        }).collect(Collectors.toList());

                    monthlyStats.setMonthlyStats(deviceStatsList);
                    return monthlyStats;
                }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取设备任务统计失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<FhMediaResourceVO> getFlightTaskMediaList(String taskUuid) {
        try {
            // 1. 构建请求URL
            String url = String.format(FhOpenapiPathConstant.FLIGHT_TASK_MEDIA, taskUuid);

            // 2. 调用大疆API获取媒体资源列表
            String response = fhOpenApiHttpUtil.get(url);
            if (response == null || response.isEmpty()) {
                log.error("获取飞行任务媒体资源列表失败，API返回空响应");
                return Collections.emptyList();
            }

            // 3. 解析响应数据
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            if (jsonResponse == null) {
                log.error("解析API响应失败，响应数据：{}", response);
                return Collections.emptyList();
            }

            // 检查响应码
            Integer code = jsonResponse.getInt("code", -1);
            if (code != 0) {
                return Collections.emptyList();
            }

            // 4. 提取分页信息和媒体资源列表
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                log.error("API返回数据格式不正确，缺少data字段");
                return Collections.emptyList();
            }

            JSONArray mediaArray = data.getJSONArray("list");
            if (CollUtil.isEmpty(mediaArray)) {
                log.info("未找到媒体资源数据");
                return Collections.emptyList();
            }

            // 5. 转换媒体资源数据
            List<FhMediaResourceVO> mediaList = new ArrayList<>(mediaArray.size());
            for (int i = 0; i < mediaArray.size(); i++) {
                try {
                    JSONObject mediaJson = mediaArray.getJSONObject(i);
                    FhMediaResourceVO media = JSONUtil.toBean(mediaJson, FhMediaResourceVO.class);

                    mediaList.add(media);
                } catch (Exception e) {
                    log.error("解析媒体资源数据失败，索引：{}", i, e);
                }
            }
            return mediaList;

        } catch (Exception e) {
            log.error("获取飞行任务媒体资源列表异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> createFlightTask(FhFlightTaskCreateDTO createDTO) {
        log.info("创建飞行任务，参数：{}", createDTO);
        try {
            // 1. 使用JsonUtils工具类将DTO转换为蛇形命名（下划线分隔）的JSON字符串
            String requestJson = JsonUtils.toSnakeCaseJson(createDTO);
			long beginAt = System.currentTimeMillis() / 1000;

            // 设置时区为Asia/Shanghai，确保使用中国时区
            if (createDTO.getTimeZone() == null || createDTO.getTimeZone().isEmpty()) {
                createDTO.setTimeZone("Asia/Shanghai");
                // 重新生成JSON，确保时区信息被包含
                requestJson = JsonUtils.toSnakeCaseJson(createDTO);
            }

            log.info("创建飞行任务，请求JSON：{}", requestJson);

            // 2. 调用大疆API创建飞行任务
            String responseStr = fhOpenApiHttpUtil.post(FhOpenapiPathConstant.FLIGHT_TASK, requestJson);

            // 3. 解析响应数据
            JSONObject response = FhOpenApiHttpUtil.resData(responseStr);
            if (response == null) {
                return R.fail("创建飞行任务失败：API返回数据为空");
            }

            // 4. 处理响应结果 首先尝试获取单个任务ID
            String taskUUId = response.getStr("task_uuid");
            if (taskUUId != null && !taskUUId.isEmpty()) {
				FhFlightTaskRequestByTimeBaseDTO request = new FhFlightTaskRequestByTimeBaseDTO();
				request.setDeviceSn(createDTO.getSn());
				// 如果是立即起飞的任务则把开始结束时间控制一下
				if (createDTO.getTaskType().equals("immediate")) {
					request.setBeginAt(beginAt - 60);
					// 结束时间为开始时间的后3分钟
					request.setEndAt(beginAt + 3 * 60);
				} else {
					request.setBeginAt(createDTO.getBeginAt());
					request.setEndAt(createDTO.getEndAt());
				}

				request.setName(createDTO.getName());
				List<FhFlightTaskVO> deviceFlightTaskList = getDeviceFlightTaskList(request);
				saveFlightTaskList(deviceFlightTaskList, createDTO.getGridIds());
				return R.data(taskUUId, "创建飞行任务成功");
            }

            log.error("创建飞行任务失败，API返回异常：{}", response);
            return R.fail("创建飞行任务失败：API返回异常");
        } catch (Exception e) {
            log.error("创建飞行任务失败", e);
            return R.fail("创建飞行任务失败：" + e.getMessage());
        }
    }

	@Override
    public List<FhWaylineVO> getWaylineList() {
        try {
            // 调用大疆API获取航线列表
            String response = FhOpenApiHttpUtil.get(FhOpenapiPathConstant.WAYLINES);
            log.debug("获取航线列表原始响应：{}", response);

            // 解析响应数据
            if (response == null || response.isEmpty()) {
                log.warn("获取航线列表返回数据为空");
                return Collections.emptyList();
            }

            // 解析响应JSON
            JSONObject jsonResponse = JSONUtil.parseObj(response);

            // 检查响应码
            Integer code = jsonResponse.getInt("code", -1);
            if (code != 0) {
                log.warn("获取航线列表API返回错误码：{}, 错误信息：{}", code, jsonResponse.getStr("message"));
                return Collections.emptyList();
            }

            // 获取data.list数组
            if (!jsonResponse.containsKey("data") || !jsonResponse.getJSONObject("data").containsKey("list")) {
                log.warn("获取航线列表返回数据格式不正确，缺少data.list字段");
                return Collections.emptyList();
            }

            JSONArray waylineArray = jsonResponse.getJSONObject("data").getJSONArray("list");
            if (CollUtil.isEmpty(waylineArray)) {
                log.info("航线列表为空");
                return Collections.emptyList();
            }

            // 使用JSONUtil直接将JSONArray转换为对象列表
            List<FhWaylineVO> waylineList = JSONUtil.toList(waylineArray, FhWaylineVO.class);

            // 处理特殊字段和嵌套对象
            waylineList.forEach(wayline -> {
                // 设置ID字段（确保waylineId与id一致）
                wayline.setWaylineId(wayline.getId());
                wayline.setFileSize(wayline.getSize());

                // 处理更新时间
                if (wayline.getUpdateTimeStamp() != null) {
                    wayline.setUpdateTime(LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(wayline.getUpdateTimeStamp()),
                        ZoneId.systemDefault()));
                }

                // 兼容旧版本，设置第一个模板类型
                if (CollUtil.isNotEmpty(wayline.getTemplateTypes())) {
                    wayline.setTemplateType(wayline.getTemplateTypes().get(0));
                }
            });

            log.info("获取航线列表成功，共{}条记录", waylineList.size());
            return waylineList;
        } catch (Exception e) {
            log.error("获取航线列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public R<String> updateFlightTaskStatus(FhFlightTaskStatusUpdateDTO updateDTO) {
		log.info("更新飞行任务状态，任务UUID：{}，状态：{}", updateDTO.getTaskUuid(), updateDTO.getStatus());
		try {
			// 1. 参数校验
			if (StrUtil.isBlank(updateDTO.getTaskUuid())) {
				return R.fail("任务UUID 不能为空");
			}
			if (StrUtil.isBlank(updateDTO.getStatus())) {
				return R.fail("任务状态 不能为空");
			}

			// 2. 构建请求 URL
			String url = String.format(FhOpenapiPathConstant.FLIGHT_TASK_STATUS, updateDTO.getTaskUuid());

			// 3. 构建请求参数为蛇形命名 JSON，JsonUtils 内部已实现驼峰转蛇形
			String requestJson = JsonUtils.toSnakeCaseJson(Collections.singletonMap("status", updateDTO.getStatus()));

			// 4. 调用大疆 API 更新飞行任务状态
			String responseStr = fhOpenApiHttpUtil.put(url, requestJson);

			// 5. 处理响应结果
			if (responseStr != null && responseStr.contains("Error request, response status: 500")) {
				log.error("更新飞行任务状态失败，服务器返回500错误，任务UUID：{}", updateDTO.getTaskUuid());
				return R.fail("更新飞行任务状态失败：服务器内部错误");
			}

			// 6. 解析响应数据（工具方法内部已做空值/格式校验）
			com.alibaba.fastjson.JSONObject response = JSON.parseObject(responseStr);
			int code = response.getIntValue("code");
			String message = response.getString("message");

			if (code != 0) {
				return R.fail(code, "更新飞行任务状态失败：" + message);
			}

			// 7. 更新数据库中的飞行任务状态
			try {
				// 根据UUID查询飞行任务
				FhFlightTask flightTask = fhFlightTaskMapper.selectByUuid(updateDTO.getTaskUuid());
				if (flightTask != null) {
					// 更新任务状态
					flightTask.setTaskStatus(updateDTO.getStatus());
					fhFlightTaskMapper.updateById(flightTask);
					log.info("成功更新数据库中飞行任务状态，任务UUID：{}，状态：{}", updateDTO.getTaskUuid(), updateDTO.getStatus());
				} else {
					log.warn("未找到对应的飞行任务记录，无法更新状态，任务UUID：{}", updateDTO.getTaskUuid());
				}
			} catch (Exception e) {
				// 数据库更新失败不影响API调用结果
				log.error("更新数据库中飞行任务状态失败，任务UUID：{}", updateDTO.getTaskUuid(), e);
			}

			return R.success("OK");

		} catch (Exception e) {
			log.error("更新飞行任务状态出现异常，任务UUID：{}", updateDTO.getTaskUuid(), e);
			return R.fail("更新飞行任务状态异常：" + e.getMessage());
		}
    }

    /**
     * 获取所有设备在指定年份的月度任务统计
     *
     * @param deviceList 设备列表
     * @param year 年份
     * @return 设备SN -> (月份 -> 任务数量) 的映射
     */
    private Map<String, Map<Integer, Long>> getAllDevicesMonthlyTasks(List<FhOAPrjDeviceVO> deviceList, Integer year) {
        try {
            // 提取所有drone设备的SN号
            List<String> droneSNList = deviceList.stream()
                .filter(device -> device.getDrone() != null && device.getDrone().getSn() != null)
                .map(device -> device.getDrone().getSn())
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(droneSNList)) {
                log.info("未找到有效的无人机设备");
                return Collections.emptyMap();
            }

            // 计算年份的开始和结束时间
            Year targetYear = Year.of(year);
            OffsetDateTime startDate = targetYear.atDay(1)
                .atStartOfDay()
                .atZone(ZoneId.systemDefault())
                .toOffsetDateTime();

            OffsetDateTime endDate = targetYear.atMonth(12)
                .atEndOfMonth()
                .atTime(23, 59, 59)
                .atZone(ZoneId.systemDefault())
                .toOffsetDateTime();

            // 构建查询条件并执行查询
            List<FhFlightTask> allTasksList = fhFlightTaskMapper.selectList(
                new LambdaQueryWrapper<FhFlightTask>()
                    .in(FhFlightTask::getSn, droneSNList)
                    .ge(FhFlightTask::getBeginAt, startDate)
                    .le(FhFlightTask::getBeginAt, endDate)
                    .eq(FhFlightTask::getIsDeleted, 0)
            );

            // 使用Stream API按设备SN和月份分组统计任务数量
            return allTasksList.stream()
                .collect(Collectors.groupingBy(FhFlightTask::getSn, Collectors.groupingBy(
                        task -> task.getBeginAt().getMonthValue(),
                        Collectors.counting())));
        } catch (Exception e) {
            log.error("获取所有设备在[{}]年的月度任务统计失败", year, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public List<FhFlightTaskVO> getFlightTaskListByGrid(FhFlightTaskGridQueryDTO requestDTO) {
        LambdaQueryWrapper<FhFlightTask> queryWrapper = new LambdaQueryWrapper<>();

        // 根据网格ID过滤
        if (StrUtil.isNotEmpty(requestDTO.getGridId())) {
            // 使用FIND_IN_SET函数或自定义条件实现精确匹配
            // 避免like查询导致的误匹配问题（如查询ID为1时误匹配到11、12等ID）
            String gridId = requestDTO.getGridId();
            queryWrapper.and(wrapper -> wrapper
                .eq(FhFlightTask::getGridIds, gridId)
                .or()
                .likeRight(FhFlightTask::getGridIds, gridId + ",")
                .or()
                .likeLeft(FhFlightTask::getGridIds, "," + gridId)
                .or()
                .like(FhFlightTask::getGridIds, "," + gridId + ",")
            );
        }

        // 根据时间范围过滤
        if (requestDTO.getBeginAt() != null && requestDTO.getEndAt() != null) {
            // 使用ZoneId.systemDefault()获取系统默认时区
            ZoneId zoneId = ZoneId.of("Asia/Shanghai");
            // 将秒级时间戳转换为OffsetDateTime，确保使用正确的时区
            OffsetDateTime beginTime = OffsetDateTime.ofInstant(
                java.time.Instant.ofEpochSecond(requestDTO.getBeginAt()), zoneId);
            OffsetDateTime endTime = OffsetDateTime.ofInstant(
                java.time.Instant.ofEpochSecond(requestDTO.getEndAt()), zoneId);
            // 记录转换后的时间，便于调试
            log.debug("时间范围过滤：beginTime={}, endTime={}", beginTime, endTime);
            queryWrapper.between(FhFlightTask::getBeginAt, beginTime, endTime);
        }

        // 根据任务类型过滤
        if (requestDTO.getTaskType() != null) {
            queryWrapper.eq(FhFlightTask::getTaskType, requestDTO.getTaskType().name());
        }

        // 根据任务状态过滤
        if (requestDTO.getStatus() != null && requestDTO.getStatus().length > 0) {
            queryWrapper.in(FhFlightTask::getTaskStatus, Arrays.asList(requestDTO.getStatus()));
        }

        // 执行查询
        List<FhFlightTask> taskList = fhFlightTaskMapper.selectList(queryWrapper);

        // 将实体列表转换为VO列表
		List<FhFlightTaskVO> collect = taskList.stream().map(entity -> {
			FhFlightTaskVO vo = new FhFlightTaskVO();
			BeanUtils.copyProperties(entity, vo);
			// 可能需要手动复制一些特殊字段或进行格式转换
			vo.setBeginAt(String.valueOf(entity.getBeginAt()));
			vo.setEndAt(String.valueOf(entity.getEndAt()));
			return vo;
		}).collect(Collectors.toList());

		return collect;
    }

	@Override
	public FhFlightTaskVO getFlightTaskById(String id) {
		// 构建API请求URL - 使用飞行任务详情接口
		String url = String.format(FhOpenapiPathConstant.FLIGHT_TASK_DETAIL, id);
		// 2. 调用大疆API获取媒体资源列表
		String response = fhOpenApiHttpUtil.get(url);
		if (StrUtil.isNotBlank(response)) {
			JSONObject data = FhOpenApiHttpUtil.resData(response);
			return data.toBean(FhFlightTaskVO.class);
		}
		return null;
	}
}
