package org.springblade.modules.fh.mqtt.api.events;

public class FileUploadCallback {

    private Integer result;

    private Integer progress;

    private FileUploadCallbackFile file;

    private FileFlightTask flight_task;

    public FileUploadCallback() {
    }

    @Override
    public String toString() {
        return "FileUploadCallback{" +
                "result=" + result +
                ", progress=" + progress +
                ", file=" + file +
                ", flight_task=" + flight_task +
                '}';
    }

    public Integer getResult() {
        return result;
    }

    public FileUploadCallback setResult(Integer result) {
        this.result = result;
        return this;
    }

    public Integer getProgress() {
        return progress;
    }

    public FileUploadCallback setProgress(Integer progress) {
        this.progress = progress;
        return this;
    }

    public FileUploadCallbackFile getFile() {
        return file;
    }

    public FileUploadCallback setFile(FileUploadCallbackFile file) {
        this.file = file;
        return this;
    }

    public FileFlightTask getFlight_task() {
        return flight_task;
    }

    public void setFlight_task(FileFlightTask flight_task) {
        this.flight_task = flight_task;
    }
}
