package org.springblade.modules.fh.service.impl;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.FhOpenapiPathConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.fh.pojo.dto.FhWsTokenDTO;
import org.springblade.modules.fh.pojo.vo.FhOAPrjDeviceVO;
import org.springblade.modules.fh.pojo.vo.FhWsTokenVO;
import org.springblade.modules.fh.service.IFhOrgService;
import org.springblade.modules.fh.utils.EjFhServerHttpUtil;
import org.springblade.modules.fh.utils.FhOpenApiHttpUtil;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FhOrgServiceImpl implements IFhOrgService {

	@Override
	public List<FhOAPrjDeviceVO> prjDevices() {

		String res = FhOpenApiHttpUtil.get(FhOpenapiPathConstant.PROJECT_DEVICES);

		JSONObject obj = FhOpenApiHttpUtil.resData(res);

		return obj.getBeanList("list",FhOAPrjDeviceVO.class);
	}

	@Override
	public FhWsTokenVO prjWsToken() {

		UrlBuilder urlBuilder = EjFhServerHttpUtil.urlBuilderCreate()
			.addPath("/dji/fh/ws-token");

		FhWsTokenDTO.FhWsTokenDTOBuilder builder = FhWsTokenDTO.builder()
			.orgId(EjFhServerHttpUtil.getFhOrgUuid())
			.prjId(EjFhServerHttpUtil.getFhPrjUuid())
			.userId(AuthUtil.getUserId());

		String res = EjFhServerHttpUtil.post(urlBuilder.build(), JSONUtil.toJsonStr(builder.build()));

		try{
			return JSONUtil.toBean(res, FhWsTokenVO.class);
		}catch (Exception e){
			throw new ServiceException("获取ws异常");
		}
	}
}
