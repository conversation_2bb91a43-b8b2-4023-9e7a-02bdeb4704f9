package org.springblade.modules.fh.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备模型信息视图对象
 *
 * <AUTHOR> AI
 */
@Data
@Schema(description = "设备模型信息")
public class DeviceModelVO {
    /**
     * 设备模型键值
     */
    @Schema(description = "设备模型键值")
    private String key;

    /**
     * 设备域
     */
    @Schema(description = "设备域")
    private String domain;

    /**
     * 设备类型
     */
    @Schema(description = "设备类型")
    private String type;

    /**
     * 设备子类型
     */
    @Schema(description = "设备子类型")
    @JsonProperty("sub_type")
    private String subType;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String name;

    /**
     * 设备类别
     */
    @Schema(description = "设备类别")
    @JsonProperty("class")
    private String deviceClass;
}
