/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.common.cache.*;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.enums.DictEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.enums.StatusType;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.oauth2.exception.OAuth2Exception;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.BladeTenantProperties;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.*;
import org.springblade.modules.auth.provider.UserType;
import org.springblade.modules.beachwaste.service.IGridScheduleService;
import org.springblade.modules.system.excel.SimpleUserExcel;
import org.springblade.modules.system.excel.UserExcel;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.pojo.entity.*;
import org.springblade.modules.system.pojo.vo.UserVO;
import org.springblade.modules.system.service.IRoleService;
import org.springblade.modules.system.service.IUserDeptService;
import org.springblade.modules.system.service.IUserOauthService;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.common.constant.CommonConstant.DEFAULT_PARAM_PASSWORD;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserService {
	private static final String GUEST_NAME = "guest";
	private static final String[] SIMPLE_EXCEL_HEADERS = {"登录账号", "所属角色", "用户姓名", "手机号码"};
	private static final int[] SIMPLE_EXCEL_COLUMN_WIDTHS = {20, 20, 20, 20};

	@Override
	public void exportSimpleTemplate(HttpServletResponse response) {
		try (Workbook workbook = new XSSFWorkbook()) {
			// 创建工作表
			Sheet sheet = workbook.createSheet("用户数据模板");

			// 创建标题行样式
			CellStyle headerStyle = workbook.createCellStyle();
			Font headerFont = workbook.createFont();
			headerFont.setBold(true);
			headerStyle.setFont(headerFont);
			headerStyle.setAlignment(HorizontalAlignment.CENTER);
			headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

			// 创建标题行
			Row headerRow = sheet.createRow(0);
			for (int i = 0; i < SIMPLE_EXCEL_HEADERS.length; i++) {
				Cell cell = headerRow.createCell(i);
				cell.setCellValue(SIMPLE_EXCEL_HEADERS[i]);
				cell.setCellStyle(headerStyle);
				sheet.setColumnWidth(i, SIMPLE_EXCEL_COLUMN_WIDTHS[i] * 256);
			}

			// 创建示例数据行
			Row dataRow = sheet.createRow(1);
			dataRow.createCell(0).setCellValue("example_account");
			dataRow.createCell(1).setCellValue("管理员");
			dataRow.createCell(2).setCellValue("张三");
			dataRow.createCell(3).setCellValue("***********");

			// 设置响应头
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("用户导入模板.xlsx", "UTF-8"));

			// 写入响应流
			workbook.write(response.getOutputStream());
		} catch (IOException e) {
			throw new ServiceException("导出用户模板失败：" + e.getMessage());
		}
	}

	private final IUserDeptService userDeptService;
	private final IUserOauthService userOauthService;
	private final IRoleService roleService;
	private final BladeTenantProperties tenantProperties;
	private final BladeRedis bladeRedis;
	private final IGridScheduleService gridScheduleService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(User user) {
		if (StringUtil.isBlank(user.getTenantId())) {
			user.setTenantId(BladeConstant.ADMIN_TENANT_ID);
		}
		String tenantId = user.getTenantId();
		Tenant tenant = SysCache.getTenant(tenantId);
		if (Func.isNotEmpty(tenant)) {
			Integer accountNumber = tenant.getAccountNumber();
			if (tenantProperties.getLicense()) {
				String licenseKey = tenant.getLicenseKey();
				String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
				accountNumber = JsonUtil.parse(decrypt, Tenant.class).getAccountNumber();
			}
			Long tenantCount = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId));
			if (accountNumber != null && accountNumber > 0 && accountNumber <= tenantCount) {
				throw new ServiceException("当前租户已到最大账号额度!");
			}
		}
		if (Func.isNotEmpty(user.getPassword())) {
			user.setPassword(DigestUtil.encrypt(user.getPassword()));
		}
		Long userCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, tenantId)
				.eq(User::getAccount, user.getAccount())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
		);
		if (userCount > 0L && Func.isEmpty(user.getId())) {
			throw new ServiceException(StringUtil.format("当前租户下已存在用户名为 [{}] 的账号!", user.getAccount()));
		}
		Long phoneCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, tenantId)
				.eq(User::getPhone, user.getPhone())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
		);
		if (phoneCount > 0L && StringUtil.isNotBlank(user.getPhone())) {
			throw new ServiceException(StringUtil.format("当前手机 [{}] 已存在!", user.getPhone()));
		}
		return save(user) && submitUserDept(user);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateUser(User user) {
		String tenantId = user.getTenantId();
		Long userCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, tenantId)
				.eq(User::getAccount, user.getAccount())
				.ne(User::getId, user.getId())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
		);
		if (userCount > 0L) {
			throw new ServiceException(StringUtil.format("当前租户下已存在用户名为 [{}] 的账号!", user.getAccount()));
		}
		Long phoneCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, tenantId)
				.eq(User::getPhone, user.getPhone())
				.ne(User::getId, user.getId())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
		);
		if (phoneCount > 0L && StringUtil.isNotBlank(user.getPhone())) {
			throw new ServiceException(StringUtil.format("手机号 [{}] 已被注册，请更换其他手机号", user.getPhone()));
		}
		return updateUserInfo(user) && submitUserDept(user);
	}

	@Override
	public boolean updateUserInfo(User user) {
		// 检查用户名是否已存在
		Long userCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, AuthUtil.getTenantId())
				.eq(User::getAccount, user.getAccount())
				.ne(User::getId, user.getId())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
		);
		if (userCount > 0L) {
			throw new ServiceException(StringUtil.format("当前租户下已存在用户名为 [{}] 的账号!", user.getAccount()));
		}

		// 检查手机号是否已存在
		Long phoneCount = baseMapper.selectCount(
			Wrappers.<User>query().lambda()
				.eq(User::getTenantId, AuthUtil.getTenantId())
				.eq(User::getPhone, user.getPhone())
				.ne(User::getId, user.getId())
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
		);
		if (phoneCount > 0L && StringUtil.isNotBlank(user.getPhone())) {
			throw new ServiceException(StringUtil.format("手机号 [{}] 已被注册，请更换其他手机号", user.getPhone()));
		}
		user.setPassword(null);
		return updateById(user);
	}

	private boolean submitUserDept(User user) {
		List<Long> deptIdList = Func.toLongList(user.getDeptId());
		List<UserDept> userDeptList = new ArrayList<>();
		deptIdList.forEach(deptId -> {
			UserDept userDept = new UserDept();
			userDept.setUserId(user.getId());
			userDept.setDeptId(deptId);
			userDeptList.add(userDept);
		});
		userDeptService.remove(Wrappers.<UserDept>update().lambda().eq(UserDept::getUserId, user.getId()));
		return userDeptService.saveBatch(userDeptList);
	}

	@Override
	public IPage<User> selectUserPage(IPage<User> page, User user, Long deptId, String tenantId) {
		List<Long> deptIdList = SysCache.getDeptChildIds(deptId);
		return page.setRecords(baseMapper.selectUserPage(page, user, deptIdList, tenantId));
	}

	@Override
	public IPage<UserVO> selectUserSearch(UserVO user, Query query) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>query().lambda();
		String tenantId = AuthUtil.getTenantId();
		if (StringUtil.isNotBlank(tenantId)) {
			queryWrapper.eq(User::getTenantId, tenantId);
		}
		if (StringUtil.isNotBlank(user.getName())) {
			queryWrapper.like(User::getName, user.getName());
		}
		if (StringUtil.isNotBlank(user.getDeptName())) {
			String deptIds = SysCache.getDeptIdsByFuzzy(AuthUtil.getTenantId(), user.getDeptName());
			if (StringUtil.isNotBlank(deptIds)) {
				queryWrapper.and(wrapper -> {
					List<String> ids = Func.toStrList(deptIds);
					ids.forEach(id -> wrapper.like(User::getDeptId, id).or());
				});
			}
		}
		if (StringUtil.isNotBlank(user.getPostName())) {
			String postIds = SysCache.getPostIdsByFuzzy(AuthUtil.getTenantId(), user.getPostName());
			if (StringUtil.isNotBlank(postIds)) {
				queryWrapper.and(wrapper -> {
					List<String> ids = Func.toStrList(postIds);
					ids.forEach(id -> wrapper.like(User::getPostId, id).or());
				});
			}
		}
		IPage<User> pages = this.page(Condition.getPage(query), queryWrapper);
		return UserWrapper.build().pageVO(pages);
	}

	@Override
	public User userByAccount(String tenantId, String account) {
		return baseMapper.selectOne(Wrappers.<User>query().lambda().eq(User::getTenantId, tenantId).eq(User::getAccount, account).eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED));
	}

	@Override
	public UserInfo userInfo(Long userId) {
		User user = baseMapper.selectById(userId);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(Long userId, UserType userType) {
		User user = baseMapper.selectById(userId);
		return buildUserInfo(user, userType);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account) {
		User user = baseMapper.getUser(tenantId, account);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, UserType userType) {
		User user = baseMapper.getUser(tenantId, account);
		return buildUserInfo(user, userType);
	}

	@Override
	public UserInfo userInfoByPhone(String tenantId, String phone) {
		User user = baseMapper.getUserByPhone(tenantId, phone);
		return buildUserInfo(user);
	}

	@Override
	public UserInfo userInfoByPhone(String tenantId, String phone, UserType userType) {
		User user = baseMapper.getUserByPhone(tenantId, phone);
		return buildUserInfo(user, userType);
	}

	private UserInfo buildUserInfo(User user) {
		return buildUserInfo(user, UserType.WEB);
	}

	private UserInfo buildUserInfo(User user, UserType userType) {
		if (ObjectUtil.isEmpty(user)) {
			return null;
		}
		UserInfo userInfo = new UserInfo();
		userInfo.setUser(user);
		if (Func.isNotEmpty(user)) {
			List<String> roleAlias = roleService.getRoleAliases(user.getRoleId());
			userInfo.setRoles(roleAlias);
		}
		// 根据每个用户平台，建立对应的detail表，通过查询将结果集写入到detail字段
		Kv detail = Kv.create().set("type", userType.getName());
		if (userType == UserType.WEB) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		} else if (userType == UserType.APP) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				detail.set("ext", query.getUserExt());
			}
		}
		userInfo.setDetail(detail);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo userInfo(UserOauth userOauth) {
		UserOauth uo = userOauthService.getOne(Wrappers.<UserOauth>query().lambda().eq(UserOauth::getUuid, userOauth.getUuid()).eq(UserOauth::getSource, userOauth.getSource()));
		UserInfo userInfo;
		if (Func.isNotEmpty(uo) && Func.isNotEmpty(uo.getUserId())) {
			userInfo = this.userInfo(uo.getUserId());
			userInfo.setOauthId(Func.toStr(uo.getId()));
		} else {
			userInfo = new UserInfo();
			if (Func.isEmpty(uo)) {
				userOauthService.save(userOauth);
				userInfo.setOauthId(Func.toStr(userOauth.getId()));
			} else {
				userInfo.setOauthId(Func.toStr(uo.getId()));
			}
			User user = new User();
			user.setAccount(userOauth.getUsername());
			user.setTenantId(userOauth.getTenantId());
			userInfo.setUser(user);
			userInfo.setRoles(Collections.singletonList(GUEST_NAME));
		}
		return userInfo;
	}

	@Override
	public boolean grant(String userIds, String roleIds) {
		User user = new User();
		user.setRoleId(roleIds);
		return this.update(user, Wrappers.<User>update().lambda().in(User::getId, Func.toLongList(userIds)));
	}

	@Override
	public boolean resetPassword(String userIds) {
		User user = new User();
		user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD));
		user.setUpdateTime(DateUtil.now());
		return this.update(user, Wrappers.<User>update().lambda().in(User::getId, Func.toLongList(userIds)));
	}

	@Override
	public boolean updatePassword(Long userId, String oldPassword, String newPassword, String newPassword1) {
		User user = getById(userId);
		if (!newPassword.equals(newPassword1)) {
			throw new ServiceException("请输入正确的确认密码!");
		}
		if (!user.getPassword().equals(DigestUtil.hex(oldPassword))) {
			throw new ServiceException("原密码不正确!");
		}
		return this.update(Wrappers.<User>update().lambda().set(User::getPassword, DigestUtil.hex(newPassword)).eq(User::getId, userId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeUser(String userIds) {
		if (Func.contains(Func.toLongArray(userIds), AuthUtil.getUserId())) {
			throw new ServiceException("不能删除本账号!");
		}

		// 获取要删除的用户列表
		List<Long> userIdList = Func.toLongList(userIds);
		List<User> userList = this.listByIds(userIdList);

		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();

		// 获取网格员角色ID
		String gridStaffRoleIds = null;
		try {
			gridStaffRoleIds = roleService.getRoleIdsByName(tenantId, "网格员");
		} catch (Exception e) {
			// 如果获取网格员角色ID失败，说明当前租户下没有网格员角色，忽略异常继续执行
		}

		// 如果成功获取到网格员角色ID，则检查要删除的用户中是否有网格员
		if (gridStaffRoleIds != null && !gridStaffRoleIds.isEmpty()) {
			// 将角色ID字符串转换为列表
			List<String> roleIdList = Arrays.asList(gridStaffRoleIds.split(","));

			// 遍历要删除的用户，检查是否为网格员
			for (User user : userList) {
				// 如果用户的角色ID包含网格员角色ID，则删除该用户从明天开始的所有排班任务
				if (user.getRoleId() != null && roleIdList.stream().anyMatch(roleId -> user.getRoleId().contains(roleId))) {
					// 调用网格排班服务删除该用户从明天开始的所有排班任务
					gridScheduleService.removeGridStaffScheduleFromTomorrow(user.getId());
				}
			}
		}

		boolean tempUser = this.deleteLogic(userIdList);
		boolean tempUserDept = userDeptService.remove(Wrappers.<UserDept>lambdaQuery().in(UserDept::getUserId, userIdList));
		if (tempUser && tempUserDept) {
			UserOauth userOauth = new UserOauth();
			userOauth.delete(Wrappers.<UserOauth>lambdaQuery().in(UserOauth::getUserId, userIdList));
			UserWeb userWeb = new UserWeb();
			userWeb.delete(Wrappers.<UserWeb>lambdaQuery().in(UserWeb::getUserId, userIdList));
			UserApp userApp = new UserApp();
			userApp.delete(Wrappers.<UserApp>lambdaQuery().in(UserApp::getUserId, userIdList));
			UserOther userOther = new UserOther();
			userOther.delete(Wrappers.<UserOther>lambdaQuery().in(UserOther::getUserId, userIdList));
			return true;
		} else {
			throw new ServiceException("删除用户失败!");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void importUser(List<UserExcel> data, Boolean isCovered) {
		data.forEach(userExcel -> {
			User user = Objects.requireNonNull(BeanUtil.copyProperties(userExcel, User.class));
			// 设置用户平台
			user.setUserType(Func.toInt(DictCache.getKey(DictEnum.USER_TYPE, userExcel.getUserTypeName()), 1));
			// 设置部门ID
			user.setDeptId(Func.toStrWithEmpty(SysCache.getDeptIds(userExcel.getTenantId(), userExcel.getDeptName()), StringPool.EMPTY));
			// 设置岗位ID
			user.setPostId(Func.toStrWithEmpty(SysCache.getPostIds(userExcel.getTenantId(), userExcel.getPostName()), StringPool.EMPTY));
			// 设置角色ID
			user.setRoleId(Func.toStrWithEmpty(SysCache.getRoleIds(userExcel.getTenantId(), userExcel.getRoleName()), StringPool.EMPTY));
			// 设置租户ID
			if (!AuthUtil.isAdministrator() || StringUtil.isBlank(user.getTenantId())) {
				user.setTenantId(AuthUtil.getTenantId());
			}
			// 覆盖数据
			if (isCovered) {
				// 查询用户是否存在
				User oldUser = UserCache.getUser(userExcel.getTenantId(), userExcel.getAccount());
				if (oldUser != null && oldUser.getId() != null) {
					user.setId(oldUser.getId());
					this.updateUser(user);
					return;
				}
			}
			// 获取默认密码配置
			String initPassword = ParamCache.getValue(DEFAULT_PARAM_PASSWORD);
			user.setPassword(initPassword);
			this.submit(user);
		});
	}

	@Override
	public List<UserExcel> exportUser(Wrapper<User> queryWrapper) {
		List<UserExcel> userList = baseMapper.exportUser(queryWrapper);
		userList.forEach(user -> {
			user.setUserTypeName(DictCache.getValue(DictEnum.USER_TYPE, user.getUserType()));
			user.setRoleName(StringUtil.join(SysCache.getRoleNames(user.getRoleId())));
			user.setDeptName(StringUtil.join(SysCache.getDeptNames(user.getDeptId())));
			user.setPostName(StringUtil.join(SysCache.getPostNames(user.getPostId())));
		});
		return userList;
	}

	@Override
	public void exportUserWithPOI(HttpServletResponse response, Wrapper<User> queryWrapper) {
		try {
			// 获取用户数据
			List<UserExcel> userList = this.exportUser(queryWrapper);

			// 创建工作簿
			Workbook workbook = new XSSFWorkbook();
			Sheet sheet = workbook.createSheet("用户数据表");

			// 创建标题行
			Row headerRow = sheet.createRow(0);
			String[] headers = {"租户编号", "用户平台名称", "账户", "昵称", "姓名", "邮箱", "手机", "角色名称", "部门名称", "岗位名称", "生日"};

			// 创建标题样式
			CellStyle headerStyle = workbook.createCellStyle();
			headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
			headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			headerStyle.setBorderBottom(BorderStyle.THIN);
			headerStyle.setBorderTop(BorderStyle.THIN);
			headerStyle.setBorderRight(BorderStyle.THIN);
			headerStyle.setBorderLeft(BorderStyle.THIN);

			Font headerFont = workbook.createFont();
			headerFont.setBold(true);
			headerStyle.setFont(headerFont);

			// 设置标题
			for (int i = 0; i < headers.length; i++) {
				Cell cell = headerRow.createCell(i);
				cell.setCellValue(headers[i]);
				cell.setCellStyle(headerStyle);
			}

			// 创建数据样式
			CellStyle dataStyle = workbook.createCellStyle();
			dataStyle.setBorderBottom(BorderStyle.THIN);
			dataStyle.setBorderTop(BorderStyle.THIN);
			dataStyle.setBorderRight(BorderStyle.THIN);
			dataStyle.setBorderLeft(BorderStyle.THIN);

			// 日期格式样式
			CellStyle dateStyle = workbook.createCellStyle();
			dateStyle.cloneStyleFrom(dataStyle);
			CreationHelper createHelper = workbook.getCreationHelper();
			dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-mm-dd"));

			// 填充数据
			for (int i = 0; i < userList.size(); i++) {
				UserExcel userExcel = userList.get(i);
				Row row = sheet.createRow(i + 1);

				// 租户编号
				Cell cell0 = row.createCell(0);
				cell0.setCellValue(userExcel.getTenantId() != null ? userExcel.getTenantId() : "");
				cell0.setCellStyle(dataStyle);

				// 用户平台名称
				Cell cell1 = row.createCell(1);
				cell1.setCellValue(userExcel.getUserTypeName() != null ? userExcel.getUserTypeName() : "");
				cell1.setCellStyle(dataStyle);

				// 账户
				Cell cell2 = row.createCell(2);
				cell2.setCellValue(userExcel.getAccount() != null ? userExcel.getAccount() : "");
				cell2.setCellStyle(dataStyle);

				// 昵称
				Cell cell3 = row.createCell(3);
				cell3.setCellValue(userExcel.getName() != null ? userExcel.getName() : "");
				cell3.setCellStyle(dataStyle);

				// 姓名
				Cell cell4 = row.createCell(4);
				cell4.setCellValue(userExcel.getRealName() != null ? userExcel.getRealName() : "");
				cell4.setCellStyle(dataStyle);

				// 邮箱
				Cell cell5 = row.createCell(5);
				cell5.setCellValue(userExcel.getEmail() != null ? userExcel.getEmail() : "");
				cell5.setCellStyle(dataStyle);

				// 手机
				Cell cell6 = row.createCell(6);
				cell6.setCellValue(userExcel.getPhone() != null ? userExcel.getPhone() : "");
				cell6.setCellStyle(dataStyle);

				// 角色名称
				Cell cell7 = row.createCell(7);
				cell7.setCellValue(userExcel.getRoleName() != null ? userExcel.getRoleName() : "");
				cell7.setCellStyle(dataStyle);

				// 部门名称
				Cell cell8 = row.createCell(8);
				cell8.setCellValue(userExcel.getDeptName() != null ? userExcel.getDeptName() : "");
				cell8.setCellStyle(dataStyle);

				// 岗位名称
				Cell cell9 = row.createCell(9);
				cell9.setCellValue(userExcel.getPostName() != null ? userExcel.getPostName() : "");
				cell9.setCellStyle(dataStyle);

				// 生日
				Cell cell10 = row.createCell(10);
				if (userExcel.getBirthday() != null) {
					cell10.setCellValue(userExcel.getBirthday());
					cell10.setCellStyle(dateStyle);
				} else {
					cell10.setCellValue("");
					cell10.setCellStyle(dataStyle);
				}
			}

			// 手动设置列宽（避免在无头环境中使用autoSizeColumn导致字体问题）
			int[] columnWidths = {3000, 4000, 3000, 3000, 3000, 5000, 4000, 6000, 6000, 6000, 3000};
			for (int i = 0; i < headers.length; i++) {
				// 设置预定义的列宽，如果超出数组范围则使用默认宽度
				int width = i < columnWidths.length ? columnWidths[i] : 3000;
				sheet.setColumnWidth(i, width);
			}

			// 设置响应头
			String fileName = "用户数据" + DateUtil.time() + ".xlsx";
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

			// 写入响应流
			workbook.write(response.getOutputStream());
			workbook.close();

		} catch (IOException e) {
			throw new RuntimeException("导出Excel文件失败", e);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean registerGuest(User user, Long oauthId) {
		Tenant tenant = SysCache.getTenant(user.getTenantId());
		if (tenant == null || tenant.getId() == null) {
			throw new ServiceException("租户信息错误!");
		}
		UserOauth userOauth = userOauthService.getById(oauthId);
		if (userOauth == null || userOauth.getId() == null) {
			throw new ServiceException("第三方登陆信息错误!");
		}
		user.setRealName(user.getName());
		user.setAvatar(userOauth.getAvatar());
		user.setRoleId(StringPool.MINUS_ONE);
		user.setDeptId(StringPool.MINUS_ONE);
		user.setPostId(StringPool.MINUS_ONE);
		user.setStatus(StatusType.IN_ACTIVE.getType());
		boolean userTemp = this.submit(user);
		userOauth.setUserId(user.getId());
		userOauth.setTenantId(user.getTenantId());
		boolean oauthTemp = userOauthService.updateById(userOauth);
		return (userTemp && oauthTemp);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean registerUser(User user) {
		Tenant tenant = SysCache.getTenant(user.getTenantId());
		if (tenant == null || tenant.getId() == null) {
			throw new OAuth2Exception("租户信息错误!");
		}
		user.setRealName(user.getName());
		user.setRoleId(StringPool.MINUS_ONE);
		user.setDeptId(StringPool.MINUS_ONE);
		user.setPostId(StringPool.MINUS_ONE);
		user.setStatus(StatusType.IN_ACTIVE.getType());
		return this.submit(user);
	}

	@Override
	public boolean updatePlatform(Long userId, Integer userType, String userExt) {
		if (userType.equals(UserType.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userWeb.setId(query.getId());
			}
			userWeb.setUserId(userId);
			userWeb.setUserExt(userExt);
			return userWeb.insertOrUpdate();
		} else if (userType.equals(UserType.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userApp.setId(query.getId());
			}
			userApp.setUserId(userId);
			userApp.setUserExt(userExt);
			return userApp.insertOrUpdate();
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, userId));
			if (ObjectUtil.isNotEmpty(query)) {
				userOther.setId(query.getId());
			}
			userOther.setUserId(userId);
			userOther.setUserExt(userExt);
			return userOther.insertOrUpdate();
		}
	}

	@Override
	public UserVO platformDetail(User user) {
		User detail = baseMapper.selectOne(Condition.getQueryWrapper(user));
		UserVO userVO = UserWrapper.build().entityVO(detail);
		if (userVO.getUserType().equals(UserType.WEB.getCategory())) {
			UserWeb userWeb = new UserWeb();
			UserWeb query = userWeb.selectOne(Wrappers.<UserWeb>lambdaQuery().eq(UserWeb::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else if (userVO.getUserType().equals(UserType.APP.getCategory())) {
			UserApp userApp = new UserApp();
			UserApp query = userApp.selectOne(Wrappers.<UserApp>lambdaQuery().eq(UserApp::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		} else {
			UserOther userOther = new UserOther();
			UserOther query = userOther.selectOne(Wrappers.<UserOther>lambdaQuery().eq(UserOther::getUserId, user.getId()));
			if (ObjectUtil.isNotEmpty(query)) {
				userVO.setUserExt(query.getUserExt());
			}
		}
		return userVO;
	}

	@Override
	public boolean unlock(String userIds) {
		if (StringUtil.isBlank(userIds)) {
			throw new ServiceException("请至少选择一个用户!");
		}
		List<User> userList = baseMapper.selectList(Wrappers.<User>lambdaQuery().in(User::getId, Func.toLongList(userIds)));
		userList.forEach(user -> bladeRedis.del(CacheNames.tenantKey(user.getTenantId(), CacheNames.USER_FAIL_KEY, user.getAccount())));
		return true;
	}

	@Override
	public boolean auditPass(String userIds) {
		if (StringUtil.isBlank(userIds)) {
			throw new ServiceException("请至少选择一个用户!");
		}
		List<User> userList = baseMapper.selectList(Wrappers.<User>lambdaQuery().in(User::getId, Func.toLongList(userIds)));
		userList.forEach(user -> {
			boolean roleTemp = StringUtil.isBlank(user.getRoleId()) || user.getRoleId().equals(StringPool.MINUS_ONE);
			boolean deptTemp = StringUtil.isBlank(user.getDeptId()) || user.getDeptId().equals(StringPool.MINUS_ONE);
			boolean postTemp = StringUtil.isBlank(user.getPostId()) || user.getPostId().equals(StringPool.MINUS_ONE);
			if (roleTemp || deptTemp || postTemp) {
				throw new ServiceException("请先给账号 [" + user.getAccount() + "] 分配角色、部门、岗位后再审批通过!");
			}
		});
		return changeStatus(Func.toLongList(userIds), StatusType.ACTIVE.getType());
	}

	@Override
	public boolean auditRefuse(String userIds) {
		if (StringUtil.isBlank(userIds)) {
			throw new ServiceException("请至少选择一个用户!");
		}
		return changeStatus(Func.toLongList(userIds), StatusType.DISABLED.getType());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void importSimpleUser(List<SimpleUserExcel> data, Boolean isCovered, String tenantId) {
		// 需要更新的用户列表
		List<User> updateUserList = new ArrayList<>();
		// 需要新增的用户列表
		List<User> insertUserList = new ArrayList<>();

		// 获取所有账号，用于批量查询
		List<String> accountList = data.stream().map(SimpleUserExcel::getAccount).collect(Collectors.toList());

		// 如果是覆盖模式，先批量查询已存在的用户
		Map<String, User> existUserMap;
		if (isCovered && !accountList.isEmpty()) {
			List<User> existUsers = baseMapper.selectList(
				Wrappers.<User>query().lambda()
					.eq(User::getTenantId, tenantId)
					.in(User::getAccount, accountList)
					.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			);
			existUserMap = existUsers.stream().collect(Collectors.toMap(User::getAccount, user -> user));
		} else {
			existUserMap = new HashMap<>();
		}

		// 处理每条数据
		data.forEach(userExcel -> {
			// 校验手机号格式
			String phone = userExcel.getPhone();
			if (StringUtil.isNotBlank(phone) && !phone.matches("^1[3-9]\\d{9}$")) {
				throw new ServiceException(StringUtil.format("手机号 [{}] 格式不正确!", phone));
			}

			// 判断是更新还是新增
			if (isCovered && existUserMap.containsKey(userExcel.getAccount())) {
				User existUser = existUserMap.get(userExcel.getAccount());

				// 手机号重复校验 (更新场景)
				validatePhoneNumberUniqueness(tenantId, phone, existUser.getId(), userExcel.getAccount(), true);

				User user = createUserFromExcel(userExcel, tenantId, phone);
				user.setId(existUser.getId()); // 设置 ID 用于更新
				updateUserList.add(user);
			} else {
				// 用户名重复校验 (新增场景)
				validateUsernameUniqueness(tenantId, userExcel.getAccount(), isCovered);

				// 手机号重复校验 (新增场景)
				validatePhoneNumberUniqueness(tenantId, phone, null, userExcel.getAccount(), false);

				// 只有在非覆盖模式下用户名校验通过，或者覆盖模式下用户不存在时，才进行新增
				if (!isCovered || !existUserMap.containsKey(userExcel.getAccount())) {
					User user = createUserFromExcel(userExcel, tenantId, phone);
					insertUserList.add(user);
				}
			}
		});

		// 批量更新
		if (!updateUserList.isEmpty()) {
			this.updateBatchById(updateUserList);
		}

		// 批量新增
		if (!insertUserList.isEmpty()) {
			this.saveBatch(insertUserList);
		}
	}

	/**
	 * 从Excel数据创建User对象
	 *
	 * @param userExcel Excel数据
	 * @param tenantId  租户ID
	 * @param phone     校验后的手机号
	 * @return User对象
	 */
	private User createUserFromExcel(SimpleUserExcel userExcel, String tenantId, String phone) {
		User user = new User();
		user.setAccount(userExcel.getAccount());
		user.setRealName(userExcel.getRealName());
		// 使用校验后的手机号
		user.setPhone(phone);
		user.setName(userExcel.getRealName());
		user.setTenantId(tenantId);
		user.setRoleId(Func.toStrWithEmpty(SysCache.getRoleIds(tenantId, userExcel.getRoleName()), StringPool.EMPTY));

		// 密码使用HTLJ+手机号码 加密后
		String passwordPrefix = "HTLJ";
		// 注意：即使手机号为空，也尝试加密（虽然结果可能固定，但保持逻辑一致性）
		String passwordToEncrypt = passwordPrefix + (StringUtil.isNotBlank(phone) ? phone : "");
		user.setPassword(DigestUtil.encrypt(passwordToEncrypt));
		return user;
	}

	/**
	 * 校验手机号唯一性
	 *
	 * @param tenantId  租户ID
	 * @param phone     手机号
	 * @param userId    用户ID (更新时传入，用于排除自身)
	 * @param account   账号 (用于错误提示)
	 * @param isUpdate  是否为更新操作
	 */
	private void validatePhoneNumberUniqueness(String tenantId, String phone, Long userId, String account, boolean isUpdate) {
		if (StringUtil.isNotBlank(phone)) {
			LambdaQueryWrapper<User> wrapper = Wrappers.<User>query().lambda()
				.eq(User::getTenantId, tenantId)
				.eq(User::getPhone, phone)
				.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED);

			// 更新时排除自身
			if (isUpdate && userId != null) {
				wrapper.ne(User::getId, userId);
			}

			Long phoneCount = baseMapper.selectCount(wrapper);
			if (phoneCount > 0L) {
				String errorMsg = StringUtil.format("手机号 [{}] 已被注册，请更换其他手机号", phone);
				throw new ServiceException(errorMsg);
			}
		}
	}

	/**
	 * 校验用户名唯一性 (新增场景)
	 *
	 * @param tenantId  租户ID
	 * @param account   账号
	 * @param isCovered 是否为覆盖模式
	 */
	private void validateUsernameUniqueness(String tenantId, String account, Boolean isCovered) {
		// 仅在非覆盖模式下需要校验新增时的用户名重复
		if (!isCovered) {
			Long userCount = baseMapper.selectCount(
				Wrappers.<User>query().lambda()
					.eq(User::getTenantId, tenantId)
					.eq(User::getAccount, account)
					.eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			);
			if (userCount > 0L) {
				throw new ServiceException(StringUtil.format("新增账号失败：当前租户下已存在用户名为 [{}] 的账号!", account));
			}
		}
	}

}
